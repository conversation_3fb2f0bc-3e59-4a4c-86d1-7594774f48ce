#include "spotify/spotifytrack.h"
#include <QJsonArray>
#include <QJsonDocument>
#include <QDebug>

SpotifyTrack::SpotifyTrack(QObject *parent)
    : QObject(parent)
    , m_durationMs(0)
    , m_popularity(0)
    , m_explicit(false)
    , m_playable(true)
    , m_tempo(0.0f)
    , m_energy(0.0f)
    , m_danceability(0.0f)
    , m_valence(0.0f)
    , m_audioFeaturesLoaded(false)
{
}

SpotifyTrack::SpotifyTrack(const QJsonObject &trackData, QObject *parent)
    : SpotifyTrack(parent)
{
    parseTrackData(trackData);
}

QString SpotifyTrack::id() const
{
    return m_id;
}

QString SpotifyTrack::name() const
{
    return m_name;
}

QString SpotifyTrack::uri() const
{
    return m_uri;
}

QUrl SpotifyTrack::externalUrl() const
{
    return m_externalUrl;
}

QUrl SpotifyTrack::previewUrl() const
{
    return m_previewUrl;
}

int SpotifyTrack::durationMs() const
{
    return m_durationMs;
}

int SpotifyTrack::popularity() const
{
    return m_popularity;
}

bool SpotifyTrack::isExplicit() const
{
    return m_explicit;
}

bool SpotifyTrack::isPlayable() const
{
    return m_playable;
}

QStringList SpotifyTrack::artistNames() const
{
    return m_artistNames;
}

QStringList SpotifyTrack::artistIds() const
{
    return m_artistIds;
}

QString SpotifyTrack::primaryArtist() const
{
    return m_artistNames.isEmpty() ? "Unknown Artist" : m_artistNames.first();
}

QString SpotifyTrack::albumName() const
{
    return m_albumName;
}

QString SpotifyTrack::albumId() const
{
    return m_albumId;
}

QUrl SpotifyTrack::albumImageUrl(const QString &size) const
{
    if (size == "small") return m_albumImageSmall;
    if (size == "large") return m_albumImageLarge;
    return m_albumImageMedium; // default to medium
}

QString SpotifyTrack::releaseDate() const
{
    return m_releaseDate;
}

QString SpotifyTrack::displayText() const
{
    if (m_artistNames.isEmpty()) {
        return m_name;
    }
    return QString("%1 - %2").arg(m_artistNames.join(", "), m_name);
}

QString SpotifyTrack::durationText() const
{
    return formatDuration(m_durationMs);
}

bool SpotifyTrack::isValid() const
{
    return !m_id.isEmpty() && !m_name.isEmpty();
}

void SpotifyTrack::updateFromJson(const QJsonObject &trackData)
{
    parseTrackData(trackData);
    emit trackUpdated();
}

void SpotifyTrack::setPreviewUrl(const QUrl &url)
{
    m_previewUrl = url;
    qDebug() << "Manually set preview URL to:" << m_previewUrl.toString();
}

void SpotifyTrack::parseTrackData(const QJsonObject &trackData)
{
    m_id = trackData["id"].toString();
    m_name = trackData["name"].toString();
    m_uri = trackData["uri"].toString();
    m_durationMs = trackData["duration_ms"].toInt();
    m_popularity = trackData["popularity"].toInt();
    m_explicit = trackData["explicit"].toBool();
    m_playable = trackData["is_playable"].toBool(true);

    // External URLs
    QJsonObject externalUrls = trackData["external_urls"].toObject();
    if (externalUrls.contains("spotify")) {
        m_externalUrl = QUrl(externalUrls["spotify"].toString());
    }

    // Preview URL
    qDebug() << "Parsing preview_url from JSON:";
    qDebug() << "  preview_url exists:" << trackData.contains("preview_url");
    qDebug() << "  preview_url isNull:" << trackData["preview_url"].isNull();
    qDebug() << "  preview_url value:" << trackData["preview_url"].toString();

    if (!trackData["preview_url"].isNull()) {
        QString previewUrlString = trackData["preview_url"].toString();
        m_previewUrl = QUrl(previewUrlString);
        qDebug() << "  Set preview URL to:" << m_previewUrl.toString();
        qDebug() << "  Preview URL is valid:" << m_previewUrl.isValid();
    } else {
        qDebug() << "  Preview URL is null, not setting";
    }

    // Parse artists
    if (trackData.contains("artists")) {
        parseArtists(trackData["artists"].toArray());
    }

    // Parse album
    if (trackData.contains("album")) {
        parseAlbum(trackData["album"].toObject());
    }
}

void SpotifyTrack::parseArtists(const QJsonArray &artists)
{
    m_artistNames.clear();
    m_artistIds.clear();

    for (const QJsonValue &artistValue : artists) {
        QJsonObject artist = artistValue.toObject();
        m_artistNames.append(artist["name"].toString());
        m_artistIds.append(artist["id"].toString());
    }
}

void SpotifyTrack::parseAlbum(const QJsonObject &album)
{
    m_albumName = album["name"].toString();
    m_albumId = album["id"].toString();
    m_releaseDate = album["release_date"].toString();

    // Parse album images
    QJsonArray images = album["images"].toArray();
    for (const QJsonValue &imageValue : images) {
        QJsonObject image = imageValue.toObject();
        int height = image["height"].toInt();
        QUrl imageUrl(image["url"].toString());

        // Categorize by size (Spotify typically provides 640x640, 300x300, 64x64)
        if (height >= 500) {
            m_albumImageLarge = imageUrl;
        } else if (height >= 200) {
            m_albumImageMedium = imageUrl;
        } else {
            m_albumImageSmall = imageUrl;
        }
    }
}

QString SpotifyTrack::formatDuration(int milliseconds) const
{
    int seconds = milliseconds / 1000;
    int minutes = seconds / 60;
    seconds %= 60;
    return QString("%1:%2").arg(minutes).arg(seconds, 2, 10, QChar('0'));
}

SpotifyTrack* SpotifyTrack::fromJson(const QJsonObject &json, QObject *parent)
{
    return new SpotifyTrack(json, parent);
}

QList<SpotifyTrack*> SpotifyTrack::fromJsonArray(const QJsonArray &array, QObject *parent)
{
    QList<SpotifyTrack*> tracks;
    for (const QJsonValue &value : array) {
        if (value.isObject()) {
            tracks.append(new SpotifyTrack(value.toObject(), parent));
        }
    }
    return tracks;
}

QJsonObject SpotifyTrack::toJson() const
{
    QJsonObject json;
    json["id"] = m_id;
    json["name"] = m_name;
    json["uri"] = m_uri;
    json["duration_ms"] = m_durationMs;
    json["popularity"] = m_popularity;
    json["explicit"] = m_explicit;
    json["is_playable"] = m_playable;
    
    if (!m_externalUrl.isEmpty()) {
        QJsonObject externalUrls;
        externalUrls["spotify"] = m_externalUrl.toString();
        json["external_urls"] = externalUrls;
    }
    
    if (!m_previewUrl.isEmpty()) {
        json["preview_url"] = m_previewUrl.toString();
    }

    return json;
}

// Audio features getters (will be implemented when we add audio features support)
QString SpotifyTrack::key() const { return m_key; }
QString SpotifyTrack::mode() const { return m_mode; }
float SpotifyTrack::tempo() const { return m_tempo; }
float SpotifyTrack::energy() const { return m_energy; }
float SpotifyTrack::danceability() const { return m_danceability; }
float SpotifyTrack::valence() const { return m_valence; }
