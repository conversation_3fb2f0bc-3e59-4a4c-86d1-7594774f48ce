#include "audioplayer.h"
#include <QDebug>

AudioPlayer::AudioPlayer(QObject *parent)
    : QObject(parent)
    , m_mediaPlayer(new QMediaPlayer(this))
    , m_audioOutput(new QAudioOutput(this))
    , m_playbackState(StoppedState)
{
    // Connect media player to audio output
    m_mediaPlayer->setAudioOutput(m_audioOutput);
    
    // Set default volume
    m_audioOutput->setVolume(0.7f);
    
    // Connect signals
    connect(m_mediaPlayer, &QMediaPlayer::positionChanged,
            this, &AudioPlayer::positionChanged);
    connect(m_mediaPlayer, &QMediaPlayer::durationChanged,
            this, &AudioPlayer::durationChanged);
    connect(m_mediaPlayer, &QMediaPlayer::mediaStatusChanged,
            this, &AudioPlayer::onMediaStatusChanged);
    connect(m_mediaPlayer, &QMediaPlayer::playbackStateChanged,
            this, &AudioPlayer::onPlaybackStateChanged);
    connect(m_mediaPlayer, &QMediaPlayer::errorOccurred,
            this, &AudioPlayer::onErrorOccurred);
    
    connect(m_audioOutput, &QAudioOutput::volumeChanged,
            this, &AudioPlayer::volumeChanged);
    connect(m_audioOutput, &QAudioOutput::mutedChanged,
            this, &AudioPlayer::mutedChanged);
}

AudioPlayer::~AudioPlayer()
{
    stop();
}

void AudioPlayer::play()
{
    m_mediaPlayer->play();
}

void AudioPlayer::pause()
{
    m_mediaPlayer->pause();
}

void AudioPlayer::stop()
{
    m_mediaPlayer->stop();
}

void AudioPlayer::setSource(const QUrl &url)
{
    m_mediaPlayer->setSource(url);
}

qint64 AudioPlayer::position() const
{
    return m_mediaPlayer->position();
}

qint64 AudioPlayer::duration() const
{
    return m_mediaPlayer->duration();
}

void AudioPlayer::setPosition(qint64 position)
{
    m_mediaPlayer->setPosition(position);
}

float AudioPlayer::volume() const
{
    return m_audioOutput->volume();
}

void AudioPlayer::setVolume(float volume)
{
    m_audioOutput->setVolume(qBound(0.0f, volume, 1.0f));
}

bool AudioPlayer::isMuted() const
{
    return m_audioOutput->isMuted();
}

void AudioPlayer::setMuted(bool muted)
{
    m_audioOutput->setMuted(muted);
}

AudioPlayer::PlaybackState AudioPlayer::playbackState() const
{
    return m_playbackState;
}

QMediaPlayer::MediaStatus AudioPlayer::mediaStatus() const
{
    return m_mediaPlayer->mediaStatus();
}

QString AudioPlayer::errorString() const
{
    return m_mediaPlayer->errorString();
}

void AudioPlayer::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    emit mediaStatusChanged(status);
    
    if (status == QMediaPlayer::InvalidMedia) {
        emit errorOccurred("Invalid media file");
    }
}

void AudioPlayer::onPlaybackStateChanged(QMediaPlayer::PlaybackState state)
{
    Q_UNUSED(state)
    updatePlaybackState();
}

void AudioPlayer::onErrorOccurred(QMediaPlayer::Error error, const QString &errorString)
{
    Q_UNUSED(error)
    emit errorOccurred(errorString);
}

void AudioPlayer::updatePlaybackState()
{
    PlaybackState newState = StoppedState;
    
    switch (m_mediaPlayer->playbackState()) {
    case QMediaPlayer::PlayingState:
        newState = PlayingState;
        break;
    case QMediaPlayer::PausedState:
        newState = PausedState;
        break;
    case QMediaPlayer::StoppedState:
        newState = StoppedState;
        break;
    }
    
    if (m_playbackState != newState) {
        m_playbackState = newState;
        emit playbackStateChanged(m_playbackState);
    }
}
