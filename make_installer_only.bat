@echo off
echo ========================================
echo Audio Player - Build Installer Only
echo (Application must be already built)
echo ========================================
echo.

REM Set paths
set NSIS_DIR=C:\Program Files (x86)\NSIS

echo [1/3] Checking prerequisites...

REM Check if NSIS is installed
if not exist "%NSIS_DIR%\makensis.exe" (
    echo ERROR: NSIS not found at %NSIS_DIR%
    echo Please install NSIS from https://nsis.sourceforge.io/
    echo Or update the NSIS_DIR path in this script
    pause
    exit /b 1
)

REM Check if application is built
if not exist "build\release\AudioPlayer.exe" (
    echo ERROR: AudioPlayer.exe not found in build\release\
    echo Please build the application first using:
    echo   - Qt Creator, or
    echo   - build_installer.bat, or
    echo   - qmake + mingw32-make
    pause
    exit /b 1
)

echo Prerequisites check passed!
echo.

echo [2/3] Removing old installer...
if exist "AudioPlayer_Setup.exe" (
    del /q "AudioPlayer_Setup.exe"
    echo Old installer removed.
)

echo [3/3] Building installer with NSIS...
echo Running makensis...
"%NSIS_DIR%\makensis.exe" installer.nsi
if errorlevel 1 (
    echo ERROR: NSIS installer build failed
    echo Check the installer.nsi file for errors
    echo Make sure all required DLL files exist in their specified locations
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS! Installer built successfully!
echo ========================================
echo.
echo Installer file: AudioPlayer_Setup.exe
echo Size: 
for %%A in (AudioPlayer_Setup.exe) do echo %%~zA bytes

echo.
echo The installer is ready for distribution!
echo.
pause
