/* Dark Theme Stylesheet for Audio Player */

/* Main Window */
QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}

QWidget {
    background-color: #2b2b2b;
    color: #ffffff;
    font-family: "Segoe UI", Aria<PERSON>, sans-serif;
}

/* Buttons */
QPushButton {
    background-color: #404040;
    border: 1px solid #555555;
    border-radius: 6px;
    padding: 8px 16px;
    min-width: 80px;
    font-weight: 500;
}

QPushButton:hover {
    background-color: #505050;
    border-color: #777777;
}

QPushButton:pressed {
    background-color: #353535;
    border-color: #333333;
}

QPushButton:disabled {
    background-color: #2a2a2a;
    border-color: #3a3a3a;
    color: #666666;
}

/* Control Buttons (Play, Pause, etc.) */
QPushButton#playPauseButton,
QPushButton#stopButton,
QPushButton#nextButton,
QPushButton#previousButton,
QPushButton#muteButton {
    background-color: #404040;
    border: 2px solid #555555;
    border-radius: 50%;
    padding: 8px;
}

QPushButton#playPauseButton:hover,
QPushButton#stopButton:hover,
QPushButton#nextButton:hover,
QPushButton#previousButton:hover,
QPushButton#muteButton:hover {
    background-color: #2196F3;
    border-color: #1976D2;
}

QPushButton#playPauseButton:pressed,
QPushButton#stopButton:pressed,
QPushButton#nextButton:pressed,
QPushButton#previousButton:pressed,
QPushButton#muteButton:pressed {
    background-color: #1565C0;
    border-color: #0D47A1;
}

/* Sliders */
QSlider::groove:horizontal {
    border: 1px solid #999999;
    height: 8px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #404040, stop:1 #353535);
    margin: 2px 0;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #42A5F5, stop:1 #2196F3);
    border: 1px solid #1976D2;
    width: 18px;
    margin: -2px 0;
    border-radius: 9px;
}

QSlider::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #64B5F6, stop:1 #42A5F5);
    border-color: #1E88E5;
}

QSlider::handle:horizontal:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1976D2, stop:1 #1565C0);
    border-color: #0D47A1;
}

QSlider::sub-page:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #2196F3, stop:1 #1976D2);
    border: 1px solid #1565C0;
    height: 8px;
    border-radius: 4px;
}

/* List View (Playlist) */
QListView {
    background-color: #353535;
    border: 1px solid #555555;
    border-radius: 6px;
    alternate-background-color: #404040;
    selection-background-color: #2196F3;
    outline: none;
}

QListView::item {
    padding: 10px;
    border-bottom: 1px solid #444444;
    color: #ffffff;
}

QListView::item:selected {
    background-color: #2196F3;
    color: #ffffff;
}

QListView::item:hover {
    background-color: #505050;
}

QListView::item:selected:hover {
    background-color: #1E88E5;
}

/* Labels */
QLabel {
    background-color: transparent;
    color: #ffffff;
}

QLabel#albumArtLabel {
    border: 2px solid #555555;
    border-radius: 8px;
    background-color: #404040;
    padding: 2px;
    margin: 0px auto;
}

QLabel#trackTitleLabel {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
    margin-top: 15px;
    margin-left: 10px;
    margin-right: 10px;
    text-align: center;
}

QLabel#trackArtistLabel {
    font-size: 14px;
    color: #cccccc;
    margin-top: 8px;
    margin-left: 10px;
    margin-right: 10px;
    text-align: center;
}

QLabel#trackAlbumLabel {
    font-size: 12px;
    color: #999999;
    font-style: italic;
    margin-top: 5px;
    margin-left: 10px;
    margin-right: 10px;
    text-align: center;
}

QLabel#playlistLabel {
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    padding: 5px 0;
}

QLabel#currentTimeLabel,
QLabel#totalTimeLabel {
    font-family: "Consolas", "Monaco", monospace;
    font-size: 12px;
    color: #cccccc;
    min-width: 45px;
}

/* Menu Bar */
QMenuBar {
    background-color: #353535;
    border-bottom: 1px solid #555555;
    color: #ffffff;
    padding: 2px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #2196F3;
    color: #ffffff;
}

QMenuBar::item:pressed {
    background-color: #1976D2;
}

/* Menu */
QMenu {
    background-color: #353535;
    border: 1px solid #555555;
    border-radius: 6px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 24px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #2196F3;
    color: #ffffff;
}

QMenu::separator {
    height: 1px;
    background-color: #555555;
    margin: 4px 8px;
}

/* Status Bar */
QStatusBar {
    background-color: #353535;
    border-top: 1px solid #555555;
    color: #cccccc;
    padding: 4px;
}

/* Splitter */
QSplitter::handle {
    background-color: #555555;
}

QSplitter::handle:horizontal {
    width: 3px;
    border-radius: 1px;
}

QSplitter::handle:hover {
    background-color: #777777;
}

/* Scroll Bars */
QScrollBar:vertical {
    background-color: #404040;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #666666;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #777777;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QScrollBar:horizontal {
    background-color: #404040;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #666666;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #777777;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
}

/* Tooltips */
QToolTip {
    background-color: #454545;
    color: #ffffff;
    border: 1px solid #666666;
    border-radius: 4px;
    padding: 6px;
    font-size: 12px;
}
