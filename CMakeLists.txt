cmake_minimum_required(VERSION 3.16)

project(AudioPlayer VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required Qt components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Multimedia Svg)

# Enable Qt's MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Set directories
set(CMAKE_AUTOUIC_SEARCH_PATHS ui)

# Include directories
include_directories(include)

# Source files
set(SOURCES
    src/main.cpp
    src/mainwindow.cpp
    src/audioplayer.cpp
    src/playlist.cpp
    src/playlistmodel.cpp
)

# Header files
set(HEADERS
    include/mainwindow.h
    include/audioplayer.h
    include/playlist.h
    include/playlistmodel.h
)

# UI files
set(UI_FILES
    ui/mainwindow.ui
)

# Resource files
set(RESOURCES
    resources/resources.qrc
)

# Create executable
add_executable(AudioPlayer
    ${SOURCES}
    ${HEADERS}
    ${UI_FILES}
    ${RESOURCES}
)

# Link Qt libraries
target_link_libraries(AudioPlayer
    Qt6::Core
    Qt6::Widgets
    Qt6::Multimedia
    Qt6::Svg
)

# Set target properties
set_target_properties(AudioPlayer PROPERTIES
    WIN32_EXECUTABLE TRUE
    MACOSX_BUNDLE TRUE
)

# Install target
install(TARGETS AudioPlayer
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)
