/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.9.1
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // dark_theme.qss
  0x0,0x0,0x5,0x1d,
  0x0,
  0x0,0x17,0x5f,0x78,0xda,0xb5,0x58,0x6d,0x4f,0xe3,0x38,0x10,0xfe,0xde,0x5f,0x61,
  0x81,0x4e,0x5a,0x50,0x73,0x24,0xa1,0x29,0x10,0x3e,0xc1,0xb2,0x48,0x27,0x1d,0x12,
  0x2b,0xba,0xb7,0x9f,0xdd,0xc4,0xb4,0x16,0x6e,0x9c,0x73,0x9c,0x5,0xf6,0xb4,0xff,
  0x7d,0xc7,0x71,0x9c,0x97,0xc6,0x49,0x5a,0xe,0x12,0xa9,0x8a,0xed,0x74,0xfc,0xcc,
  0xcc,0x33,0xe3,0x99,0x9c,0x1c,0xa3,0x1b,0x2c,0x9e,0xd0,0x62,0x4d,0x36,0x4,0x3d,
  0xc8,0x57,0x46,0xb2,0x35,0x21,0x12,0x3d,0x72,0x81,0xae,0xf2,0x98,0x72,0x74,0xcf,
  0xf0,0x2b,0x11,0xe8,0xf8,0x64,0x32,0x39,0x39,0x46,0x77,0x98,0x26,0xe8,0x3b,0x4d,
  0x62,0xfe,0xac,0xa6,0xbe,0xaa,0x71,0x39,0xfc,0x6f,0x82,0xe0,0x5a,0xe2,0xe8,0x69,
  0x25,0x78,0x9e,0xc4,0x4e,0xc4,0x19,0x17,0x21,0x3a,0xf4,0x97,0xea,0xbe,0x2c,0x96,
  0xcd,0xdc,0x63,0x71,0x5d,0x4e,0x7e,0x4d,0x26,0x5f,0xbf,0xd3,0x78,0x5,0x5b,0xbe,
  0xe9,0xff,0x6a,0xee,0x91,0x27,0xd2,0x79,0xc4,0x1b,0xca,0x5e,0x43,0x74,0xf0,0x40,
  0x56,0x9c,0xa0,0x6f,0x7f,0x1d,0x4c,0xd1,0x95,0xa0,0x98,0x4d,0x51,0x86,0x93,0xcc,
  0xc9,0x88,0xa0,0x7a,0x3f,0xd0,0xe2,0x3a,0x97,0x92,0x27,0x59,0xa1,0xc1,0x7d,0x9e,
  0xad,0xf5,0xb8,0x1f,0xc1,0xcc,0x55,0xb7,0xde,0x6d,0xc9,0x45,0x4c,0x60,0xd2,0x4b,
  0x5f,0x50,0xc6,0x19,0x8d,0xd1,0x61,0x50,0x5c,0xcd,0x65,0x47,0xe0,0x98,0xe6,0x59,
  0x88,0xe6,0xe9,0x8b,0x9e,0x4f,0x71,0x1c,0xd3,0x64,0x15,0xa2,0x73,0xf8,0x9f,0x57,
  0x4d,0x6f,0x68,0xe2,0x3c,0xd3,0x58,0xae,0x61,0xc1,0x35,0x93,0x85,0x42,0xcf,0x84,
  0xae,0xd6,0x32,0x44,0x81,0xeb,0x6a,0x33,0xd5,0x40,0xc3,0x35,0xff,0x1,0x2e,0xe9,
  0x85,0x1b,0xb8,0xea,0x6e,0xe1,0x31,0x4b,0x67,0xc5,0xd5,0x11,0x98,0xa,0x92,0x65,
  0x24,0xee,0x17,0x79,0x1a,0xa8,0xdb,0x2a,0xf2,0xb4,0xb8,0x3a,0x22,0x63,0x9a,0xe1,
  0x25,0x1b,0x92,0xe9,0x63,0x75,0xdb,0x65,0x62,0x75,0xb7,0x5d,0x3e,0x2f,0x2e,0xe3,
  0xc2,0xcf,0x60,0x23,0xc1,0x59,0xe5,0xca,0x4f,0x8a,0xa7,0x53,0x74,0x8f,0xf3,0x8c,
  0x4c,0x11,0x91,0xd1,0x9f,0x47,0x5b,0xee,0x3d,0x4c,0xe1,0x8d,0x62,0x5d,0x8f,0xa7,
  0xad,0xc5,0x4c,0xf2,0xd4,0x36,0x9f,0x90,0x17,0x69,0x9b,0x7,0x8b,0xfd,0xa0,0x3c,
  0xcf,0x6c,0x6b,0x9b,0x5c,0x92,0xb7,0x50,0xca,0xdf,0x89,0x52,0x81,0xfb,0x47,0x97,
  0x52,0xdb,0xe6,0xdf,0x56,0x56,0x53,0xa6,0x4f,0x65,0xdb,0x6a,0xad,0xb8,0x6d,0xb5,
  0xad,0xbe,0xed,0x8d,0xda,0x8,0x63,0x74,0xf5,0xbd,0x8b,0xf9,0xed,0xa9,0x95,0x7,
  0xde,0xc5,0xd9,0xfc,0xc6,0x1f,0x55,0xae,0xa4,0x6f,0xaf,0x7a,0xd6,0xf5,0x86,0x82,
  0xd6,0xf5,0x2d,0x15,0xad,0xef,0x34,0x94,0x1c,0xd,0x21,0x2f,0x98,0x7,0x9f,0xed,
  0x51,0xe9,0xde,0xcc,0xce,0xae,0x3c,0x43,0xed,0x7,0x60,0x0,0x11,0x3a,0x3b,0xe9,
  0xe7,0x30,0x4,0x59,0x60,0x44,0xb0,0xa4,0xa0,0x3f,0x81,0xf9,0x98,0x99,0x7d,0xba,
  0xd9,0xe8,0xa2,0xb8,0xf4,0x3e,0xeb,0x32,0x89,0x9c,0x9b,0xd4,0x52,0xe3,0xa,0xd1,
  0xbf,0x8c,0x26,0x4,0x8b,0x95,0x22,0x16,0x49,0xe4,0xa7,0x17,0x2f,0x74,0xa7,0xe8,
  0xb5,0xf8,0x7d,0xf1,0x8b,0x67,0x3f,0xf4,0x20,0x7f,0x82,0x21,0x43,0xd7,0x50,0xb6,
  0x1c,0x7b,0x26,0x27,0x1c,0x95,0x89,0xc,0x4,0xd1,0x44,0x73,0xd8,0xb5,0xf2,0x76,
  0x66,0x58,0x6a,0x74,0x5a,0xe3,0x24,0x66,0x36,0x9d,0xfe,0xf,0x46,0xff,0x2a,0xb8,
  0xd,0x6a,0x8c,0x9a,0x5b,0x47,0xbd,0xa9,0xdb,0x10,0x4c,0x2d,0x97,0x79,0xd8,0xab,
  0x8c,0x65,0x74,0x72,0xfa,0x95,0xba,0x18,0x55,0xaa,0x87,0xfd,0xfb,0xab,0x36,0x9f,
  0x5d,0x7,0xb7,0xf3,0x5a,0x35,0xad,0xea,0x91,0x3d,0x6e,0xbe,0x9c,0x9f,0x7f,0x9,
  0xc6,0x90,0xf5,0x51,0x76,0x7f,0x6c,0xda,0x8c,0x35,0x36,0xcd,0xf5,0xa3,0x31,0xb2,
  0x57,0xd8,0xb2,0x7c,0xe9,0xa4,0x78,0xf5,0xce,0x64,0xd0,0xce,0x6f,0xa0,0x2a,0x50,
  0xe,0x90,0xa1,0x11,0xa1,0xdd,0xc8,0xb1,0xb3,0x19,0xe2,0xf5,0x6f,0x9a,0x49,0xf4,
  0xf,0x25,0xcf,0xfa,0x10,0x62,0x30,0xd4,0x67,0x8f,0x5a,0x28,0xe6,0xf7,0x38,0x56,
  0xf7,0x2e,0x2c,0x30,0x93,0x44,0x24,0x58,0x12,0x67,0xe4,0x94,0xc9,0x8,0x23,0x91,
  0xa4,0x3c,0x71,0x46,0x72,0x30,0xcf,0xa5,0x32,0x74,0x88,0x12,0x9e,0x10,0xed,0x27,
  0xa3,0x49,0x18,0x52,0x49,0x36,0xa5,0x3e,0xd5,0xf9,0xe3,0xb9,0x5b,0x46,0x5a,0x72,
  0x48,0x89,0x9b,0x96,0x2a,0xb3,0xe2,0xea,0x2f,0x2,0xdb,0x3b,0x84,0x1a,0xec,0x60,
  0xf5,0xd0,0x40,0xbc,0x83,0xc0,0x1d,0x4b,0xa6,0x1,0x28,0x63,0x22,0x1a,0x31,0xa7,
  0x48,0x81,0x97,0x84,0xe9,0x1c,0x5e,0x3c,0xf6,0xfe,0x4f,0xa,0x28,0x4f,0x53,0x2c,
  0x80,0xd0,0x3,0xca,0x28,0x11,0x87,0x98,0x2d,0xf3,0xcd,0x95,0x90,0x2d,0x81,0x7b,
  0xd5,0xe,0x96,0x73,0xc0,0xca,0x95,0xca,0xb5,0xfe,0x76,0x2e,0x4,0x57,0x23,0x9c,
  0x4b,0xde,0xc4,0x5,0x2a,0x44,0x4f,0xb,0x2a,0x19,0x69,0x22,0x2b,0xaa,0xd8,0x8c,
  0xfe,0x24,0xcd,0x94,0xda,0x2a,0x6d,0x97,0x9c,0xc5,0xfd,0x65,0xbd,0xde,0xd1,0x51,
  0xb1,0x8b,0xbc,0xa0,0x8d,0xc3,0x61,0xe4,0x51,0x36,0x89,0x57,0x4e,0xb,0x2d,0xb8,
  0x9e,0x97,0x70,0xc8,0x3b,0x98,0xd1,0x15,0x40,0x8f,0xc0,0xc4,0x44,0x74,0x80,0x83,
  0x41,0xc1,0xe1,0x7d,0xc8,0x67,0x46,0x92,0x81,0x18,0x15,0x57,0x17,0xe2,0xf9,0x7,
  0x22,0x54,0x7e,0xef,0x3,0xe8,0x6f,0x3,0x6c,0x9e,0xff,0xfa,0x45,0xd5,0xd6,0x85,
  0x88,0x42,0x52,0xa5,0x16,0xe0,0x1f,0x63,0xda,0xb4,0xcc,0x84,0xa3,0x76,0xdd,0x83,
  0x11,0x15,0x2b,0x3,0x7d,0x1e,0xd7,0xbb,0x45,0xb9,0x50,0x1,0xb4,0xa0,0x1b,0x4d,
  0xc1,0x69,0x65,0x3f,0xe,0x5a,0x57,0xd3,0x4d,0x1c,0x55,0xc3,0x8,0x9d,0x4,0x4,
  0xe,0xce,0xa0,0x61,0x3c,0xb8,0xe3,0x9,0x8e,0x38,0x3c,0x6d,0x78,0xc2,0x21,0x2a,
  0x23,0x72,0x39,0x6e,0xf0,0x16,0x23,0xea,0x7e,0x6e,0x16,0xd4,0x47,0xc4,0x1d,0x49,
  0x72,0x74,0x8d,0x85,0xee,0x99,0x61,0xa0,0x9e,0xf7,0x69,0xb6,0x2c,0x19,0xb5,0x19,
  0xe6,0x83,0xd6,0xf2,0x4d,0x8d,0x52,0x6e,0xdc,0x4a,0xe2,0x3b,0x24,0xa4,0x4a,0xd0,
  0x5c,0xb5,0xae,0xfe,0xc8,0x81,0xd8,0xde,0xe5,0x1d,0x12,0x79,0x5b,0xde,0x78,0x99,
  0x5d,0xb7,0xc,0xc6,0xf0,0xc6,0xe8,0x1f,0x7a,0xe,0x57,0x56,0x6a,0x19,0xc2,0x7e,
  0x60,0xaa,0x6f,0x0,0xfe,0x6c,0x17,0x43,0xbe,0xab,0x15,0xa1,0xce,0x22,0xe0,0x58,
  0x2c,0xb9,0x21,0x9f,0xa9,0x72,0xbc,0x81,0x73,0xa1,0xa9,0xbc,0x39,0x5,0x0,0x63,
  0xd5,0x75,0xaa,0x8e,0x45,0x62,0x99,0x67,0x15,0xc1,0xf5,0x70,0x5f,0x8a,0xeb,0x44,
  0x3f,0xcc,0xef,0x66,0xa8,0x75,0xc,0xae,0x80,0xa4,0x8c,0x4a,0x49,0x4a,0x18,0xe5,
  0xc0,0x94,0xbe,0x3,0xa7,0x7f,0xb9,0x55,0x51,0x92,0x6e,0xfd,0xab,0x5b,0x92,0x96,
  0xf1,0x7d,0xda,0xe3,0x3f,0xaf,0x6a,0x9,0xba,0x92,0x6,0xb,0x88,0xc6,0xb7,0x19,
  0xa5,0x4a,0x24,0x38,0x63,0xca,0xa6,0x65,0x27,0x58,0x8c,0x55,0x20,0x80,0x10,0x49,
  0x23,0xcc,0x76,0xfb,0xb8,0x60,0xba,0x9a,0xbe,0xb8,0x9d,0x57,0x70,0xab,0xd,0xc,
  0xde,0xf1,0x8d,0xcc,0x37,0x99,0xa1,0xc0,0x50,0x29,0xd1,0xf0,0xcc,0x77,0xc7,0x77,
  0xdb,0xc3,0x4c,0x4d,0x29,0xc0,0x6,0xa7,0x28,0x5b,0x8d,0x9c,0x69,0x6b,0x59,0x75,
  0x18,0xad,0xe5,0xad,0xfa,0x49,0xd7,0xba,0xdb,0x1d,0x47,0x5d,0x1,0xd7,0xa2,0x6,
  0x5a,0x14,0xab,0x3,0xaa,0x18,0x7b,0x83,0x7,0x76,0xd9,0x6b,0x57,0x1f,0x94,0x44,
  0x18,0x72,0xc1,0x68,0xd7,0xba,0xb3,0x13,0x6a,0x49,0x3d,0x6e,0xe8,0xfd,0x92,0x31,
  0xe2,0x8,0x88,0x8c,0x5,0xe7,0x4c,0xd2,0x54,0x87,0x85,0x1a,0x2c,0x68,0x3a,0xe0,
  0x8a,0x40,0xdd,0xfd,0xc7,0xa4,0x25,0xdd,0xf,0x58,0x74,0xd6,0x49,0xf7,0xf3,0x56,
  0x21,0xd3,0xac,0x11,0x7e,0x4d,0x7e,0x3,0x66,0xd,0x35,0xc3,
    // play.svg
  0x0,0x0,0x0,0xf8,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x38,0x20,0x35,0x56,0x31,0x39,0x4c,0x31,0x39,0x20,0x31,0x32,0x4c,0x38,
  0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,
  0x66,0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,
  0x68,0x3d,0x22,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,
  0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // pause.svg
  0x0,0x0,0x1,0x5,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x36,0x22,0x20,0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,
  0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,
  0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,0x22,0x31,0x34,0x22,0x20,
  0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x34,0x22,0x20,
  0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,
  0x76,0x67,0x3e,0xa,
    // previous.svg
  0x0,0x0,0x0,0xfa,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x32,0x30,0x20,0x35,0x56,0x31,0x39,0x4c,0x39,0x20,0x31,0x32,0x4c,0x32,
  0x30,0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,
  0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,
  0x3d,0x22,0x34,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x33,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x34,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // volume.svg
  0x0,0x0,0x1,0xe4,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x39,0x2e,0x30,0x37,0x20,0x34,
  0x2e,0x39,0x33,0x43,0x32,0x30,0x2e,0x39,0x20,0x36,0x2e,0x37,0x36,0x20,0x32,0x31,
  0x2e,0x39,0x20,0x39,0x2e,0x33,0x20,0x32,0x31,0x2e,0x39,0x20,0x31,0x32,0x43,0x32,
  0x31,0x2e,0x39,0x20,0x31,0x34,0x2e,0x37,0x20,0x32,0x30,0x2e,0x39,0x20,0x31,0x37,
  0x2e,0x32,0x34,0x20,0x31,0x39,0x2e,0x30,0x37,0x20,0x31,0x39,0x2e,0x30,0x37,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,
  0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x35,0x2e,0x35,0x34,0x20,0x38,0x2e,
  0x34,0x36,0x43,0x31,0x36,0x2e,0x34,0x20,0x39,0x2e,0x33,0x32,0x20,0x31,0x36,0x2e,
  0x39,0x20,0x31,0x30,0x2e,0x36,0x20,0x31,0x36,0x2e,0x39,0x20,0x31,0x32,0x43,0x31,
  0x36,0x2e,0x39,0x20,0x31,0x33,0x2e,0x34,0x20,0x31,0x36,0x2e,0x34,0x20,0x31,0x34,
  0x2e,0x36,0x38,0x20,0x31,0x35,0x2e,0x35,0x34,0x20,0x31,0x35,0x2e,0x35,0x34,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // next.svg
  0x0,0x0,0x0,0xfa,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x34,0x20,0x35,0x56,0x31,0x39,0x4c,0x31,0x35,0x20,0x31,0x32,0x4c,0x34,
  0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x31,0x37,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x33,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x34,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // default_album_art.svg
  0x0,0x0,0x4,0x97,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x30,0x30,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,
  0x30,0x30,0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,
  0x20,0x32,0x30,0x30,0x20,0x32,0x30,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x6e,0x6f,0x6e,0x65,0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,
  0x70,0x3a,0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,
  0x30,0x30,0x30,0x2f,0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,
  0x20,0x42,0x61,0x63,0x6b,0x67,0x72,0x6f,0x75,0x6e,0x64,0x20,0x2d,0x2d,0x3e,0xa,
  0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x30,0x30,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x30,0x30,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x34,0x30,0x34,0x30,0x34,0x30,0x22,0x20,
  0x72,0x78,0x3d,0x22,0x38,0x22,0x2f,0x3e,0xa,0x20,0x20,0xa,0x20,0x20,0x3c,0x21,
  0x2d,0x2d,0x20,0x56,0x69,0x6e,0x79,0x6c,0x20,0x72,0x65,0x63,0x6f,0x72,0x64,0x20,
  0x64,0x65,0x73,0x69,0x67,0x6e,0x20,0x2d,0x2d,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,
  0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x38,0x30,0x22,0x20,0x66,0x69,
  0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,
  0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,
  0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x37,0x30,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x31,0x61,0x31,0x61,0x31,0x61,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,
  0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,
  0x3d,0x22,0x36,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,
  0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,
  0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,
  0x30,0x22,0x20,0x72,0x3d,0x22,0x35,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x23,0x31,0x61,0x31,0x61,0x31,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,
  0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x34,0x30,0x22,0x20,0x66,0x69,
  0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,
  0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,
  0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x33,0x30,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x31,0x61,0x31,0x61,0x31,0x61,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,
  0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,
  0x3d,0x22,0x32,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,
  0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,
  0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,
  0x30,0x22,0x20,0x72,0x3d,0x22,0x31,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x23,0x34,0x30,0x34,0x30,0x34,0x30,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,
  0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x34,0x22,0x20,0x66,0x69,0x6c,
  0x6c,0x3d,0x22,0x23,0x36,0x36,0x36,0x36,0x36,0x36,0x22,0x2f,0x3e,0xa,0x20,0x20,
  0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0x4d,0x75,0x73,0x69,0x63,0x20,0x6e,0x6f,
  0x74,0x65,0x20,0x69,0x63,0x6f,0x6e,0x20,0x2d,0x2d,0x3e,0xa,0x20,0x20,0x3c,0x67,
  0x20,0x74,0x72,0x61,0x6e,0x73,0x66,0x6f,0x72,0x6d,0x3d,0x22,0x74,0x72,0x61,0x6e,
  0x73,0x6c,0x61,0x74,0x65,0x28,0x38,0x35,0x2c,0x20,0x37,0x30,0x29,0x22,0x3e,0xa,
  0x20,0x20,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x38,0x20,
  0x31,0x38,0x56,0x36,0x4c,0x32,0x30,0x20,0x34,0x56,0x31,0x36,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x38,0x38,0x38,0x38,0x38,0x38,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,
  0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,
  0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,
  0x20,0x63,0x78,0x3d,0x22,0x36,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x38,0x22,0x20,
  0x72,0x3d,0x22,0x32,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x38,0x38,0x38,
  0x38,0x38,0x38,0x22,0x2f,0x3e,0xa,0x20,0x20,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,
  0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x38,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,
  0x36,0x22,0x20,0x72,0x3d,0x22,0x32,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,
  0x38,0x38,0x38,0x38,0x38,0x38,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x2f,0x67,0x3e,
  0xa,0x20,0x20,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0x44,0x65,0x63,0x6f,0x72,
  0x61,0x74,0x69,0x76,0x65,0x20,0x74,0x65,0x78,0x74,0x20,0x2d,0x2d,0x3e,0xa,0x20,
  0x20,0x3c,0x74,0x65,0x78,0x74,0x20,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x79,
  0x3d,0x22,0x31,0x36,0x30,0x22,0x20,0x74,0x65,0x78,0x74,0x2d,0x61,0x6e,0x63,0x68,
  0x6f,0x72,0x3d,0x22,0x6d,0x69,0x64,0x64,0x6c,0x65,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x36,0x36,0x36,0x36,0x36,0x36,0x22,0x20,0x66,0x6f,0x6e,0x74,0x2d,
  0x66,0x61,0x6d,0x69,0x6c,0x79,0x3d,0x22,0x41,0x72,0x69,0x61,0x6c,0x2c,0x20,0x73,
  0x61,0x6e,0x73,0x2d,0x73,0x65,0x72,0x69,0x66,0x22,0x20,0x66,0x6f,0x6e,0x74,0x2d,
  0x73,0x69,0x7a,0x65,0x3d,0x22,0x31,0x32,0x22,0x20,0x66,0x6f,0x6e,0x74,0x2d,0x77,
  0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x62,0x6f,0x6c,0x64,0x22,0x3e,0x4e,0x4f,0x20,
  0x41,0x52,0x54,0x57,0x4f,0x52,0x4b,0x3c,0x2f,0x74,0x65,0x78,0x74,0x3e,0xa,0x3c,
  0x2f,0x73,0x76,0x67,0x3e,0xa,
    // add.svg
  0x0,0x0,0x0,0xe6,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x32,0x20,0x35,0x56,0x31,0x39,0x4d,0x35,0x20,0x31,0x32,0x48,0x31,
  0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,
  0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,0xa,
    // app_icon.svg
  0x0,0x0,0x0,0xf4,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x34,0x38,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x34,0x38,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x34,
  0x38,0x20,0x34,0x38,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,
  0x63,0x78,0x3d,0x22,0x32,0x34,0x22,0x20,0x63,0x79,0x3d,0x22,0x32,0x34,0x22,0x20,
  0x72,0x3d,0x22,0x32,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x32,0x31,
  0x39,0x36,0x46,0x33,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,
  0x64,0x3d,0x22,0x4d,0x31,0x38,0x20,0x31,0x34,0x56,0x33,0x34,0x4c,0x33,0x34,0x20,
  0x32,0x34,0x4c,0x31,0x38,0x20,0x31,0x34,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,
  0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // volume_mute.svg
  0x0,0x0,0x1,0x23,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x32,0x33,0x20,0x39,0x4c,0x31,0x37,
  0x20,0x31,0x35,0x4d,0x31,0x37,0x20,0x39,0x4c,0x32,0x33,0x20,0x31,0x35,0x22,0x20,
  0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,
  0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,
  0x3e,0xa,
    // remove.svg
  0x0,0x0,0x0,0xde,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x35,0x20,0x31,0x32,0x48,0x31,0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,
  0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // stop.svg
  0x0,0x0,0x0,0xca,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x36,0x22,0x20,0x79,0x3d,0x22,0x36,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x31,0x32,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x32,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // styles
  0x0,0x6,
  0x7,0xac,0x2,0xc3,
  0x0,0x73,
  0x0,0x74,0x0,0x79,0x0,0x6c,0x0,0x65,0x0,0x73,
    // dark_theme.qss
  0x0,0xe,
  0xd,0x16,0x97,0xc3,
  0x0,0x64,
  0x0,0x61,0x0,0x72,0x0,0x6b,0x0,0x5f,0x0,0x74,0x0,0x68,0x0,0x65,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x71,0x0,0x73,0x0,0x73,
    // play.svg
  0x0,0x8,
  0x2,0x8c,0x54,0x27,
  0x0,0x70,
  0x0,0x6c,0x0,0x61,0x0,0x79,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // pause.svg
  0x0,0x9,
  0xc,0x98,0xb7,0xc7,
  0x0,0x70,
  0x0,0x61,0x0,0x75,0x0,0x73,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // previous.svg
  0x0,0xc,
  0x8,0x37,0xc0,0xc7,
  0x0,0x70,
  0x0,0x72,0x0,0x65,0x0,0x76,0x0,0x69,0x0,0x6f,0x0,0x75,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume.svg
  0x0,0xa,
  0xc,0x3b,0xf6,0xa7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // next.svg
  0x0,0x8,
  0xc,0xf7,0x54,0x47,
  0x0,0x6e,
  0x0,0x65,0x0,0x78,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // default_album_art.svg
  0x0,0x15,
  0xa,0xf6,0x60,0xa7,
  0x0,0x64,
  0x0,0x65,0x0,0x66,0x0,0x61,0x0,0x75,0x0,0x6c,0x0,0x74,0x0,0x5f,0x0,0x61,0x0,0x6c,0x0,0x62,0x0,0x75,0x0,0x6d,0x0,0x5f,0x0,0x61,0x0,0x72,0x0,0x74,
  0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // add.svg
  0x0,0x7,
  0x7,0xa7,0x5a,0x7,
  0x0,0x61,
  0x0,0x64,0x0,0x64,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // app_icon.svg
  0x0,0xc,
  0x7,0x6f,0x91,0x27,
  0x0,0x61,
  0x0,0x70,0x0,0x70,0x0,0x5f,0x0,0x69,0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume_mute.svg
  0x0,0xf,
  0xb,0x22,0xfb,0xc7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x5f,0x0,0x6d,0x0,0x75,0x0,0x74,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // remove.svg
  0x0,0xa,
  0x6,0xcb,0x42,0x47,
  0x0,0x72,
  0x0,0x65,0x0,0x6d,0x0,0x6f,0x0,0x76,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // stop.svg
  0x0,0x8,
  0xb,0x63,0x55,0x87,
  0x0,0x73,
  0x0,0x74,0x0,0x6f,0x0,0x70,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0xb,0x0,0x0,0x0,0x4,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles
  0x0,0x0,0x0,0x10,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles/dark_theme.qss
  0x0,0x0,0x0,0x22,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xd9,0xa5,0x0,0xdc,
  // :/icons/play.svg
  0x0,0x0,0x0,0x44,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x5,0x21,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x6a,0xb1,
  // :/icons/remove.svg
  0x0,0x0,0x1,0x46,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x12,0xae,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x1a,0x8,
  // :/icons/app_icon.svg
  0x0,0x0,0x1,0x4,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x10,0x8f,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x2f,0xeb,
  // :/icons/add.svg
  0x0,0x0,0x0,0xf0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xf,0xa5,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x5,0xd4,
  // :/icons/previous.svg
  0x0,0x0,0x0,0x72,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x7,0x26,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xbb,0x98,
  // :/icons/default_album_art.svg
  0x0,0x0,0x0,0xc0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xb,0xa,
0x0,0x0,0x1,0x97,0xd9,0x9d,0x64,0x35,
  // :/icons/volume_mute.svg
  0x0,0x0,0x1,0x22,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x11,0x87,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xf2,0x4e,
  // :/icons/stop.svg
  0x0,0x0,0x1,0x60,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x13,0x90,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x93,0x2b,
  // :/icons/volume.svg
  0x0,0x0,0x0,0x90,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x8,0x24,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xdb,0xe5,
  // :/icons/pause.svg
  0x0,0x0,0x0,0x5a,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x6,0x1d,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x7f,0x6f,
  // :/icons/next.svg
  0x0,0x0,0x0,0xaa,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xa,0xc,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xa7,0x9c,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
