#include "playlist.h"
#include <QFileInfo>
#include <QTextStream>
#include <QDir>
#include <QRandomGenerator>
#include <QDebug>

Playlist::Playlist(QObject *parent)
    : QObject(parent)
    , m_currentIndex(-1)
    , m_shuffle(false)
    , m_repeat(false)
{
}

void Playlist::addItem(const QUrl &url)
{
    PlaylistItem item(url);
    extractMetadata(item);
    
    m_items.append(item);
    
    if (m_currentIndex == -1) {
        m_currentIndex = 0;
    }
    
    if (m_shuffle) {
        generateShuffleOrder();
    }
    
    emit itemAdded(m_items.count() - 1);
    emit playlistChanged();
}

void Playlist::addItems(const QList<QUrl> &urls)
{
    for (const QUrl &url : urls) {
        PlaylistItem item(url);
        extractMetadata(item);
        m_items.append(item);
    }
    
    if (m_currentIndex == -1 && !m_items.isEmpty()) {
        m_currentIndex = 0;
    }
    
    if (m_shuffle) {
        generateShuffleOrder();
    }
    
    emit playlistChanged();
}

void Playlist::removeItem(int index)
{
    if (index < 0 || index >= m_items.count()) {
        return;
    }
    
    m_items.removeAt(index);
    
    if (m_currentIndex >= m_items.count()) {
        m_currentIndex = m_items.count() - 1;
    }
    
    if (m_items.isEmpty()) {
        m_currentIndex = -1;
    }
    
    if (m_shuffle) {
        generateShuffleOrder();
    }
    
    emit itemRemoved(index);
    emit playlistChanged();
}

void Playlist::clear()
{
    m_items.clear();
    m_currentIndex = -1;
    m_shuffleOrder.clear();
    emit playlistChanged();
}

void Playlist::moveItem(int from, int to)
{
    if (from < 0 || from >= m_items.count() || to < 0 || to >= m_items.count()) {
        return;
    }
    
    if (from == to) {
        return;
    }
    
    PlaylistItem item = m_items.takeAt(from);
    m_items.insert(to, item);
    
    // Update current index
    if (m_currentIndex == from) {
        m_currentIndex = to;
    } else if (from < m_currentIndex && to >= m_currentIndex) {
        m_currentIndex--;
    } else if (from > m_currentIndex && to <= m_currentIndex) {
        m_currentIndex++;
    }
    
    if (m_shuffle) {
        generateShuffleOrder();
    }
    
    emit playlistChanged();
}

int Playlist::currentIndex() const
{
    return m_currentIndex;
}

void Playlist::setCurrentIndex(int index)
{
    if (index < -1 || index >= m_items.count()) {
        return;
    }
    
    if (m_currentIndex != index) {
        m_currentIndex = index;
        emit currentIndexChanged(m_currentIndex);
    }
}

bool Playlist::next()
{
    if (m_items.isEmpty()) {
        return false;
    }
    
    int nextIndex;
    
    if (m_shuffle) {
        nextIndex = getNextShuffleIndex();
    } else {
        nextIndex = m_currentIndex + 1;
        if (nextIndex >= m_items.count()) {
            if (m_repeat) {
                nextIndex = 0;
            } else {
                return false;
            }
        }
    }
    
    setCurrentIndex(nextIndex);
    return true;
}

bool Playlist::previous()
{
    if (m_items.isEmpty()) {
        return false;
    }
    
    int prevIndex;
    
    if (m_shuffle) {
        prevIndex = getPreviousShuffleIndex();
    } else {
        prevIndex = m_currentIndex - 1;
        if (prevIndex < 0) {
            if (m_repeat) {
                prevIndex = m_items.count() - 1;
            } else {
                return false;
            }
        }
    }
    
    setCurrentIndex(prevIndex);
    return true;
}

int Playlist::count() const
{
    return m_items.count();
}

PlaylistItem Playlist::item(int index) const
{
    if (index < 0 || index >= m_items.count()) {
        return PlaylistItem();
    }
    return m_items.at(index);
}

QUrl Playlist::currentUrl() const
{
    if (m_currentIndex < 0 || m_currentIndex >= m_items.count()) {
        return QUrl();
    }
    return m_items.at(m_currentIndex).url;
}

PlaylistItem Playlist::currentItem() const
{
    if (m_currentIndex < 0 || m_currentIndex >= m_items.count()) {
        return PlaylistItem();
    }
    return m_items.at(m_currentIndex);
}

bool Playlist::isEmpty() const
{
    return m_items.isEmpty();
}

bool Playlist::save(const QString &filename) const
{
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }
    
    QTextStream out(&file);
    out << "#EXTM3U\n";
    
    for (const PlaylistItem &item : m_items) {
        out << "#EXTINF:" << (item.duration / 1000) << "," << item.displayText() << "\n";
        out << item.url.toLocalFile() << "\n";
    }
    
    return true;
}

bool Playlist::load(const QString &filename)
{
    QFile file(filename);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return false;
    }
    
    clear();
    
    QTextStream in(&file);
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (line.startsWith("#")) {
            continue; // Skip comments and metadata for now
        }
        
        if (!line.isEmpty()) {
            QUrl url = QUrl::fromLocalFile(line);
            if (url.isValid()) {
                addItem(url);
            }
        }
    }
    
    return true;
}

void Playlist::setShuffle(bool enabled)
{
    if (m_shuffle != enabled) {
        m_shuffle = enabled;
        if (m_shuffle) {
            generateShuffleOrder();
        }
    }
}

bool Playlist::isShuffle() const
{
    return m_shuffle;
}

void Playlist::setRepeat(bool enabled)
{
    m_repeat = enabled;
}

bool Playlist::isRepeat() const
{
    return m_repeat;
}

void Playlist::generateShuffleOrder()
{
    m_shuffleOrder.clear();
    for (int i = 0; i < m_items.count(); ++i) {
        m_shuffleOrder.append(i);
    }
    
    // Fisher-Yates shuffle
    for (int i = m_shuffleOrder.count() - 1; i > 0; --i) {
        int j = QRandomGenerator::global()->bounded(i + 1);
        m_shuffleOrder.swapItemsAt(i, j);
    }
}

int Playlist::getNextShuffleIndex()
{
    if (m_shuffleOrder.isEmpty()) {
        generateShuffleOrder();
    }
    
    int currentPos = m_shuffleOrder.indexOf(m_currentIndex);
    int nextPos = currentPos + 1;
    
    if (nextPos >= m_shuffleOrder.count()) {
        if (m_repeat) {
            generateShuffleOrder(); // Generate new shuffle order
            nextPos = 0;
        } else {
            return m_currentIndex; // Stay at current
        }
    }
    
    return m_shuffleOrder.at(nextPos);
}

int Playlist::getPreviousShuffleIndex()
{
    if (m_shuffleOrder.isEmpty()) {
        generateShuffleOrder();
    }
    
    int currentPos = m_shuffleOrder.indexOf(m_currentIndex);
    int prevPos = currentPos - 1;
    
    if (prevPos < 0) {
        if (m_repeat) {
            prevPos = m_shuffleOrder.count() - 1;
        } else {
            return m_currentIndex; // Stay at current
        }
    }
    
    return m_shuffleOrder.at(prevPos);
}

void Playlist::extractMetadata(PlaylistItem &item)
{
    QFileInfo info(item.url.toLocalFile());

    if (item.title.isEmpty()) {
        QString baseName = info.baseName();

        // Try to parse artist and title from filename patterns like "Artist - Title"
        if (baseName.contains(" - ")) {
            QStringList parts = baseName.split(" - ", Qt::SkipEmptyParts);
            if (parts.size() >= 2) {
                item.artist = parts[0].trimmed();
                item.title = parts.mid(1).join(" - ").trimmed();
            } else {
                item.title = baseName;
            }
        } else {
            item.title = baseName;
        }
    }

    // If artist is still empty, try to extract from directory name
    if (item.artist.isEmpty()) {
        QString dirName = info.dir().dirName();
        // Check if directory name looks like an artist name (not generic folder names)
        QStringList genericNames = {"music", "songs", "audio", "downloads", "desktop"};
        if (!genericNames.contains(dirName.toLower())) {
            item.artist = dirName;
        }
    }

    // Try to extract album from parent directory if it looks like album structure
    if (item.album.isEmpty()) {
        QDir parentDir = info.dir();
        if (parentDir.cdUp()) {
            QString parentName = parentDir.dirName();
            QStringList genericNames = {"music", "songs", "audio", "downloads", "desktop", "documents"};
            if (!genericNames.contains(parentName.toLower()) && !parentName.isEmpty()) {
                item.album = parentName;
            }
        }
    }
}
