#ifndef PLAYLIST_H
#define PLAYLIST_H

#include <QObject>
#include <QUrl>
#include <QStringList>
#include <QFileInfo>

struct PlaylistItem {
    QUrl url;
    QString title;
    QString artist;
    QString album;
    QString albumArtPath;
    qint64 duration;

    PlaylistItem() : duration(0) {}
    PlaylistItem(const QUrl &u) : url(u), duration(0) {
        QFileInfo info(u.toLocalFile());
        title = info.baseName();
    }

    QString displayText() const {
        if (!artist.isEmpty()) {
            return QString("%1 - %2").arg(artist, title);
        }
        return title;
    }
};

class Playlist : public QObject
{
    Q_OBJECT

public:
    explicit Playlist(QObject *parent = nullptr);
    
    // Playlist management
    void addItem(const QUrl &url);
    void addItems(const QList<QUrl> &urls);
    void removeItem(int index);
    void clear();
    void moveItem(int from, int to);
    
    // Navigation
    int currentIndex() const;
    void setCurrentIndex(int index);
    bool next();
    bool previous();
    
    // Access
    int count() const;
    PlaylistItem item(int index) const;
    QUrl currentUrl() const;
    PlaylistItem currentItem() const;
    bool isEmpty() const;
    
    // File operations
    bool save(const QString &filename) const;
    bool load(const QString &filename);
    
    // Shuffle and repeat
    void setShuffle(bool enabled);
    bool isShuffle() const;
    void setRepeat(bool enabled);
    bool isRepeat() const;

signals:
    void currentIndexChanged(int index);
    void itemAdded(int index);
    void itemRemoved(int index);
    void playlistChanged();

private:
    QList<PlaylistItem> m_items;
    int m_currentIndex;
    bool m_shuffle;
    bool m_repeat;
    QList<int> m_shuffleOrder;
    
    void generateShuffleOrder();
    int getNextShuffleIndex();
    int getPreviousShuffleIndex();
    void extractMetadata(PlaylistItem &item);
};

#endif // PLAYLIST_H
