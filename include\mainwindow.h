#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QLabel>
#include <QSlider>
#include <QPushButton>
#include <QListView>
#include <QListWidget>
#include <QListWidgetItem>
#include <QFileDialog>
#include <QMessageBox>
#include <QKeyEvent>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QCloseEvent>
#include <QNetworkAccessManager>
#include <QNetworkReply>

QT_BEGIN_NAMESPACE
class QAction;
class QMenu;
QT_END_NAMESPACE

#include "audioplayer.h"
#include "playlist.h"
#include "playlistmodel.h"
#include "spotify/spotifyauth.h"
#include "spotify/spotifyapi.h"
#include "spotify/spotifytrack.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void keyPressEvent(QKeyEvent *event) override;
    void closeEvent(QCloseEvent *event) override;
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;

private slots:
    // Audio player slots
    void onPlaybackStateChanged(AudioPlayer::PlaybackState state);
    void onPositionChanged(qint64 position);
    void onDurationChanged(qint64 duration);
    void onVolumeChanged(float volume);
    void onMutedChanged(bool muted);
    void onErrorOccurred(const QString &error);
    
    // Control slots
    void playPause();
    void stop();
    void next();
    void previous();
    void setPosition(int position);
    void setVolume(int volume);
    void toggleMute();
    
    // Playlist slots
    void addFiles();
    void removeSelectedItems();
    void onPlaylistItemDoubleClicked(const QModelIndex &index);
    void onCurrentPlaylistItemChanged(int index);
    
    // Menu actions
    void openFiles();
    void newPlaylist();
    void openPlaylist();
    void savePlaylist();
    void savePlaylistAs();
    void about();
    
    // Timer slot
    void updatePosition();

    // Spotify slots
    void onSpotifyLoginClicked();
    void onSpotifySearchClicked();
    void onSpotifySearchTextChanged();
    void onSpotifySearchResultClicked(QListWidgetItem *item);

    // Spotify authentication slots
    void onSpotifyAuthenticationSucceeded();
    void onSpotifyAuthenticationFailed(const QString &error);
    void onSpotifyLoggedOut();

    // Spotify API slots
    void onSpotifySearchTracksFinished(const QList<SpotifyTrack*> &tracks, int total, int offset);
    void onSpotifyApiError(const QString &endpoint, int statusCode, const QString &message);
    void onSpotifyNetworkError(const QString &endpoint, const QString &error);

    // Album art download slot
    void onAlbumArtDownloaded();

private:
    Ui::MainWindow *ui;
    
    // Core components
    AudioPlayer *m_audioPlayer;
    Playlist *m_playlist;
    PlaylistModel *m_playlistModel;

    // Spotify components
    SpotifyAuth *m_spotifyAuth;
    SpotifyAPI *m_spotifyAPI;
    
    // UI update timer
    QTimer *m_positionTimer;
    
    // Current playlist file
    QString m_currentPlaylistFile;
    bool m_playlistModified;

    // Spotify integration state
    QList<SpotifyTrack*> m_currentSearchResults;
    
    // Helper methods
    void setupConnections();
    void setupSpotify();
    void updatePlayPauseButton();
    void updateTrackInfo();
    void updateAlbumArt(const PlaylistItem &item);
    void downloadAndSetAlbumArt(const QUrl &imageUrl);
    void updateTimeLabels(qint64 position, qint64 duration);
    QString formatTime(qint64 milliseconds) const;
    void loadSettings();
    void saveSettings();
    bool confirmPlaylistDiscard();
    
    // File operations
    QStringList getSupportedAudioFiles();
    QStringList getAudioFileFilters();
};

#endif // MAINWINDOW_H
