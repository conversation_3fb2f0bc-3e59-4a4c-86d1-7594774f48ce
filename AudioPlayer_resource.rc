#include <windows.h>

VS_VERSION_INFO VERSIONINFO
	FILEVERSION 1,0,0,0
	PRODUCTVERSION 1,0,0,0
	FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
	FILEFLAGS VS_FF_DEBUG
#else
	FILEFLAGS 0x0L
#endif
	FILEOS VOS_NT_WINDOWS32
	FILETYPE VFT_DLL
	FILESUBTYPE VFT2_UNKNOWN
	BEGIN
		BLOCK "StringFileInfo"
		BEGIN
			BLOCK "040904b0"
			BEGIN
				VALUE "CompanyName", "Audio Player Inc.\0"
				VALUE "FileDescription", "Modern Qt Audio Player\0"
				VALUE "FileVersion", "*******\0"
				VALUE "LegalCopyright", "Copyright 2025\0"
				VALUE "OriginalFilename", "AudioPlayer.exe\0"
				VALUE "ProductName", "Audio Player\0"
				VALUE "ProductVersion", "*******\0"
				VALUE "InternalName", "\0"
				VALUE "Comments", "\0"
				VALUE "LegalTrademarks", "\0"
			END
		END
		BLOCK "VarFileInfo"
		BEGIN
			VALUE "Translation", 0x0409, 1200
		END
	END
/* End of Version info */

