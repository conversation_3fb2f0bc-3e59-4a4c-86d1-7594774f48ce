#include "playlistmodel.h"
#include <QMimeData>
#include <QUrl>
#include <QFont>
#include <QColor>

PlaylistModel::PlaylistModel(Playlist *playlist, QObject *parent)
    : QAbstractListModel(parent)
    , m_playlist(playlist)
    , m_currentIndex(-1)
{
    connect(m_playlist, &Playlist::playlistChanged,
            this, &PlaylistModel::onPlaylistChanged);
    connect(m_playlist, &Playlist::currentIndexChanged,
            this, &PlaylistModel::onCurrentIndexChanged);
    connect(m_playlist, &Playlist::itemAdded,
            this, &PlaylistModel::onItemAdded);
    connect(m_playlist, &Playlist::itemRemoved,
            this, &PlaylistModel::onItemRemoved);
    
    m_currentIndex = m_playlist->currentIndex();
}

int PlaylistModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return m_playlist->count();
}

QVariant PlaylistModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= m_playlist->count()) {
        return QVariant();
    }
    
    PlaylistItem item = m_playlist->item(index.row());
    
    switch (role) {
    case Qt::DisplayRole:
        return item.displayText();
    case Qt::ToolTipRole:
        return item.url.toLocalFile();
    case Qt::FontRole:
        if (index.row() == m_currentIndex) {
            QFont font;
            font.setBold(true);
            return font;
        }
        break;
    case Qt::ForegroundRole:
        if (index.row() == m_currentIndex) {
            return QColor(33, 150, 243); // Blue color for current item
        }
        break;
    }
    
    return QVariant();
}

Qt::ItemFlags PlaylistModel::flags(const QModelIndex &index) const
{
    Qt::ItemFlags defaultFlags = QAbstractListModel::flags(index);
    
    if (index.isValid()) {
        return Qt::ItemIsDragEnabled | Qt::ItemIsDropEnabled | defaultFlags;
    } else {
        return Qt::ItemIsDropEnabled | defaultFlags;
    }
}

bool PlaylistModel::removeRows(int row, int count, const QModelIndex &parent)
{
    Q_UNUSED(parent)
    
    if (row < 0 || row + count > m_playlist->count()) {
        return false;
    }
    
    beginRemoveRows(QModelIndex(), row, row + count - 1);
    for (int i = 0; i < count; ++i) {
        m_playlist->removeItem(row);
    }
    endRemoveRows();
    
    return true;
}

Qt::DropActions PlaylistModel::supportedDropActions() const
{
    return Qt::MoveAction;
}

bool PlaylistModel::dropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent)
{
    Q_UNUSED(column)
    
    if (action == Qt::IgnoreAction) {
        return true;
    }
    
    if (!data->hasFormat("application/x-playlist-item")) {
        return false;
    }
    
    int beginRow;
    if (row != -1) {
        beginRow = row;
    } else if (parent.isValid()) {
        beginRow = parent.row();
    } else {
        beginRow = rowCount(QModelIndex());
    }
    
    QByteArray encodedData = data->data("application/x-playlist-item");
    QDataStream stream(&encodedData, QIODevice::ReadOnly);
    
    while (!stream.atEnd()) {
        int sourceRow;
        stream >> sourceRow;
        
        if (sourceRow < beginRow) {
            m_playlist->moveItem(sourceRow, beginRow - 1);
        } else {
            m_playlist->moveItem(sourceRow, beginRow);
        }
    }
    
    return true;
}

QMimeData *PlaylistModel::mimeData(const QModelIndexList &indexes) const
{
    QMimeData *mimeData = new QMimeData();
    QByteArray encodedData;
    QDataStream stream(&encodedData, QIODevice::WriteOnly);
    
    for (const QModelIndex &index : indexes) {
        if (index.isValid()) {
            stream << index.row();
        }
    }
    
    mimeData->setData("application/x-playlist-item", encodedData);
    return mimeData;
}

QStringList PlaylistModel::mimeTypes() const
{
    QStringList types;
    types << "application/x-playlist-item";
    return types;
}

void PlaylistModel::setCurrentIndex(int index)
{
    if (m_currentIndex != index) {
        QModelIndex oldIndex = createIndex(m_currentIndex, 0);
        QModelIndex newIndex = createIndex(index, 0);
        
        m_currentIndex = index;
        
        if (oldIndex.isValid()) {
            emit dataChanged(oldIndex, oldIndex);
        }
        if (newIndex.isValid()) {
            emit dataChanged(newIndex, newIndex);
        }
    }
}

int PlaylistModel::currentIndex() const
{
    return m_currentIndex;
}

void PlaylistModel::onPlaylistChanged()
{
    beginResetModel();
    endResetModel();
}

void PlaylistModel::onCurrentIndexChanged(int index)
{
    setCurrentIndex(index);
}

void PlaylistModel::onItemAdded(int index)
{
    beginInsertRows(QModelIndex(), index, index);
    endInsertRows();
}

void PlaylistModel::onItemRemoved(int index)
{
    beginRemoveRows(QModelIndex(), index, index);
    endRemoveRows();
}
