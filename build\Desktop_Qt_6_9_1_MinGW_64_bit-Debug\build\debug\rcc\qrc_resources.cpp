/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.9.1
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // dark_theme.qss
  0x0,0x0,0x4,0xf5,
  0x0,
  0x0,0x16,0x74,0x78,0xda,0xb5,0x58,0x5b,0x4f,0xe3,0x38,0x14,0x7e,0xef,0xaf,0xb0,
  0x40,0x2b,0xd,0xa8,0x59,0x92,0xd0,0x14,0x8,0x4f,0x65,0x18,0xa4,0x95,0x6,0x89,
  0x11,0xdd,0x9d,0x67,0x37,0x31,0xad,0x85,0x1b,0x67,0x13,0x67,0x80,0x59,0xcd,0x7f,
  0xdf,0x63,0x3b,0xce,0xad,0x4e,0xd2,0xb2,0xac,0x23,0xa1,0xd8,0x27,0x3d,0xd7,0xef,
  0x5c,0xcc,0xd9,0x29,0xba,0xc5,0xd9,0x33,0x5a,0x6e,0xc8,0x96,0xa0,0x47,0xf1,0xc6,
  0x48,0xbe,0x21,0x44,0xa0,0x27,0x9e,0xa1,0x45,0x11,0x53,0x8e,0x1e,0x18,0x7e,0x23,
  0x19,0x3a,0x3d,0x9b,0x4c,0xce,0x4e,0xd1,0x3d,0xa6,0x9,0xfa,0x4e,0x93,0x98,0xbf,
  0xc8,0xa3,0x6f,0x72,0x5f,0x6e,0xff,0x99,0x20,0x58,0x2b,0x1c,0x3d,0xaf,0x33,0x5e,
  0x24,0xb1,0x13,0x71,0xc6,0xb3,0x10,0x1d,0xfb,0x2b,0xf9,0x5c,0x2b,0xb2,0x39,0x7b,
  0x52,0xeb,0x7a,0xf2,0x6b,0x32,0xf9,0xf6,0x9d,0xc6,0x6b,0x10,0xf9,0xae,0xdf,0xcb,
  0xb3,0x27,0x9e,0x8,0xe7,0x9,0x6f,0x29,0x7b,0xb,0xd1,0xd1,0x23,0x59,0x73,0x82,
  0xfe,0xfc,0xe3,0x68,0x8a,0x16,0x19,0xc5,0x6c,0x8a,0x72,0x9c,0xe4,0x4e,0x4e,0x32,
  0xaa,0xe5,0x81,0x15,0x37,0x85,0x10,0x3c,0xc9,0x95,0x5,0xf,0x45,0xbe,0xd1,0xfb,
  0x7e,0xd,0x66,0xae,0x7c,0xb4,0xb4,0x15,0xcf,0x62,0x2,0x87,0x5e,0xfa,0x8a,0x72,
  0xce,0x68,0x8c,0x8e,0x3,0xb5,0x9a,0x64,0x27,0xc3,0x31,0x2d,0xf2,0x10,0xcd,0xd3,
  0x57,0x7d,0x9e,0xe2,0x38,0xa6,0xc9,0x3a,0x44,0x97,0xf0,0x3b,0xaf,0x3a,0xde,0xd2,
  0xc4,0x79,0xa1,0xb1,0xd8,0x0,0xc1,0x35,0x87,0xca,0xa0,0x17,0x42,0xd7,0x1b,0x11,
  0xa2,0xc0,0x75,0xb5,0x9b,0x6a,0x45,0xc3,0xd,0xff,0x1,0x21,0xe9,0x55,0x37,0x70,
  0xe5,0xd3,0xd2,0xc7,0x90,0x2e,0xd4,0xda,0x61,0x98,0x66,0x24,0xcf,0x49,0xdc,0xcf,
  0xf2,0x3c,0x90,0x8f,0x95,0xe5,0xb9,0x5a,0x3b,0x2c,0x63,0x9a,0xe3,0x15,0x1b,0xe2,
  0xe9,0x63,0xf9,0xd8,0x79,0x62,0xf9,0xb4,0x43,0x3e,0x57,0xcb,0x84,0xf0,0x33,0xf8,
  0x28,0xe3,0xac,0xa,0xe5,0x27,0x89,0xd3,0x29,0x7a,0xc0,0x45,0x4e,0xa6,0x88,0x88,
  0xe8,0xf7,0x93,0x4e,0x78,0x8f,0x53,0xf8,0x42,0xd1,0xf5,0x7e,0xda,0x22,0xe6,0x82,
  0xa7,0xb6,0xf3,0x84,0xbc,0xa,0xdb,0x39,0x78,0xec,0x7,0xe5,0x45,0x6e,0xa3,0x6d,
  0xb,0x41,0xde,0x3,0x29,0x7f,0x2f,0x48,0x5,0xee,0x6f,0xbb,0x90,0xea,0xba,0xbf,
  0x6b,0xac,0x86,0x4c,0x9f,0xc9,0x36,0x6a,0x6d,0xb8,0x8d,0xda,0x36,0xdf,0xf6,0x45,
  0xed,0x84,0x31,0xb8,0xfa,0xde,0xd5,0xfc,0xee,0xdc,0x8a,0x3,0xef,0xea,0x62,0x7e,
  0xeb,0x8f,0x1a,0x57,0xc2,0xb7,0xd7,0x3c,0x2b,0xbd,0x61,0xa0,0x95,0xde,0x31,0xd1,
  0xfa,0x4d,0xc3,0xc8,0xd1,0x14,0xf2,0x82,0x79,0xf0,0xd9,0x9e,0x95,0xee,0xed,0xec,
  0x62,0xe1,0x19,0x68,0x3f,0x2,0x2,0x48,0xa6,0xab,0x93,0x7e,0xf,0x43,0xe0,0x5,
  0x4e,0x4,0x4f,0x66,0xf4,0x27,0x20,0x1f,0x33,0x23,0x67,0xb7,0x1a,0x5d,0xa9,0xa5,
  0xe5,0x6c,0xca,0x22,0x72,0x69,0x4a,0x4b,0xad,0x57,0x88,0xfe,0x66,0x34,0x21,0x38,
  0x5b,0x4b,0x60,0x91,0x44,0x7c,0x7a,0xf5,0x42,0x77,0x8a,0xde,0xd4,0xdf,0x57,0x5f,
  0xbd,0xfb,0xa1,0x7,0xf5,0x13,0x1c,0x19,0xba,0x6,0xb2,0xe5,0xde,0x33,0x35,0xe1,
  0xa4,0x2c,0x64,0xc0,0x88,0x26,0x1a,0xc3,0xae,0x15,0xb7,0x33,0x83,0x52,0x63,0xd3,
  0x6,0x27,0x31,0xb3,0xd9,0xf4,0x5f,0x74,0xf4,0x17,0xc1,0x5d,0x50,0xeb,0xa8,0xb1,
  0x75,0xd2,0x5b,0xba,0xd,0xc0,0x24,0xb9,0xac,0xc3,0x5e,0xe5,0x2c,0x63,0x93,0xd3,
  0x6f,0xd4,0xd5,0xa8,0x51,0x3d,0xe8,0x3f,0xdc,0xb4,0xf9,0xec,0x26,0xb8,0x9b,0xd7,
  0xa6,0x69,0x53,0x4f,0xec,0x79,0xf3,0xe5,0xf2,0xf2,0x4b,0x30,0xa6,0x59,0x1f,0x64,
  0xf,0xd7,0x4d,0xbb,0xb1,0xd6,0x4d,0x63,0xfd,0x64,0xc,0xec,0x95,0x6e,0x79,0xb1,
  0x72,0x52,0xbc,0xfe,0x60,0x30,0xe8,0xe0,0x37,0xb4,0x52,0x5a,0xe,0x80,0xa1,0x91,
  0xa1,0xbb,0x99,0x63,0x47,0x33,0xe4,0xeb,0x57,0x9a,0xb,0xf4,0x17,0x25,0x2f,0xba,
  0x9,0x31,0xd8,0xea,0xde,0x23,0x9,0xea,0xfc,0x80,0xb6,0x7a,0xf0,0x60,0x81,0x99,
  0x20,0x59,0x82,0x5,0x71,0x46,0xba,0x4c,0x4e,0x18,0x89,0x4,0xe5,0x89,0x33,0x52,
  0x83,0x79,0x21,0xa4,0xa3,0x43,0x94,0xf0,0x84,0xe8,0x38,0x19,0x4b,0xc2,0x90,0xa,
  0xb2,0x2d,0xed,0xa9,0xfa,0x8f,0xe7,0x76,0x9c,0xb4,0xe2,0x50,0x12,0xb7,0x2d,0x53,
  0x66,0x6a,0xf5,0xf,0x81,0x6d,0x9,0xa1,0x56,0x76,0x70,0x7a,0x68,0x68,0xbc,0x7,
  0xc3,0x3d,0x47,0xa6,0x1,0x55,0xc6,0x58,0x34,0x72,0x4e,0x82,0x2,0xaf,0x8,0xd3,
  0x35,0x5c,0xbd,0xf6,0xfe,0x4e,0x64,0x30,0x9e,0xa6,0x38,0x3,0x40,0xf,0x18,0x23,
  0x59,0x1c,0x63,0xb6,0x2a,0xb6,0x8b,0x4c,0xb4,0x18,0x1e,0x34,0x3b,0x58,0xfa,0x80,
  0x15,0x2b,0x55,0x68,0x7d,0x53,0xdf,0xb4,0x6,0xa0,0x6c,0xf4,0xbc,0xa4,0x82,0x91,
  0xa6,0xe,0x6a,0x5e,0xcd,0xe9,0x4f,0xd2,0x2c,0x9e,0xad,0x21,0x76,0xc5,0x59,0xdc,
  0x3f,0xc0,0xeb,0x3a,0xeb,0xc8,0x2c,0x2d,0xb1,0xd4,0x91,0x8,0x36,0x43,0x4c,0xfa,
  0x44,0xce,0x8c,0x48,0xc3,0x3b,0x52,0x6b,0x97,0x77,0x60,0x63,0x2d,0x7d,0xda,0xc7,
  0xd9,0xef,0x72,0x6e,0xf6,0x56,0xfd,0xa1,0xbc,0x32,0x85,0x88,0x42,0xc1,0xa2,0x16,
  0x89,0x1d,0xf7,0xa5,0x65,0x79,0x18,0xb5,0xe4,0x0,0xe7,0x55,0xa1,0xa,0x74,0x93,
  0xaa,0xa5,0x45,0x45,0x26,0x51,0xb5,0xa4,0x5b,0x1d,0xad,0x69,0x65,0x38,0x7,0x75,
  0xab,0xe3,0xa6,0x1e,0xd5,0x2d,0xa,0xc6,0x6b,0x40,0x13,0xce,0xe1,0x16,0x75,0x74,
  0xcf,0x13,0x1c,0x71,0x78,0xdb,0xf2,0x84,0x3,0x54,0x23,0x72,0x3d,0xee,0xa9,0x56,
  0xc,0xea,0x4b,0xce,0x2c,0xa8,0xeb,0xe6,0x3d,0x49,0xa,0x74,0x83,0x33,0x7d,0x91,
  0x84,0x8d,0x7c,0x3f,0xe4,0x6,0x62,0x29,0x33,0x4d,0xec,0xf,0x7a,0xab,0x8a,0x4c,
  0x29,0xb8,0x55,0xd9,0xf6,0xc8,0xd2,0x8a,0xd1,0x5c,0xde,0xe7,0xfc,0x91,0x2e,0xd1,
  0x96,0xf2,0x1,0xd5,0xad,0xcd,0x6f,0x7c,0xf6,0xac,0xe7,0x68,0xe3,0x78,0xe3,0xf4,
  0xff,0xb5,0x39,0x55,0x5e,0x6a,0x39,0xc2,0xde,0x45,0xe4,0xc5,0xd8,0x9f,0xed,0xe3,
  0xc8,0xf,0xf5,0x22,0xc,0x1f,0x4,0x2,0x8b,0x5,0x37,0xe0,0x33,0xad,0xdf,0x1b,
  0x28,0x96,0x4d,0xe3,0xcd,0x98,0x8,0x3a,0x56,0x57,0x31,0x39,0xc6,0xb,0x2c,0x8a,
  0xbc,0x2,0xb8,0xde,0x1e,0xa,0x71,0x5d,0x13,0x87,0xf1,0xdd,0x4c,0xb5,0x1d,0x87,
  0x4b,0x45,0x52,0x46,0x85,0x20,0xa5,0x1a,0xe5,0xc6,0xcc,0x83,0x3,0x2d,0xb1,0x14,
  0xa5,0xe6,0xb4,0xce,0xaf,0x76,0xe7,0xb4,0x32,0xbf,0xcf,0x7b,0xe2,0xe7,0x55,0x73,
  0xf2,0x2e,0xa7,0xc1,0xae,0xda,0xf8,0x87,0x85,0x34,0x25,0x82,0x3b,0x3f,0x93,0x3e,
  0x2d,0xaf,0x47,0x6a,0x2f,0x13,0x1,0x98,0x8,0x1a,0x61,0xb6,0xdf,0x8d,0xdb,0x8c,
  0xfa,0x7d,0x79,0x3b,0xaf,0xd4,0xad,0x4,0x18,0x7d,0xc7,0x5,0x99,0x7f,0x54,0xc,
  0x25,0x86,0x2c,0x89,0x6,0x67,0xbe,0x3b,0x2e,0xed,0x0,0x37,0x35,0xb9,0x0,0x1a,
  0x1c,0x35,0xcb,0x19,0x3e,0xd3,0x16,0x59,0x8e,0xdd,0x2d,0x72,0x67,0xa8,0xd0,0x3,
  0x60,0x77,0xc,0xaf,0xc7,0xc2,0x9a,0xd5,0xc0,0xdc,0x6e,0xd,0x40,0x95,0x63,0xef,
  0x88,0xc0,0x3e,0xb2,0xf6,0x8d,0x41,0x9,0x84,0xa1,0x10,0x8c,0x5e,0xe5,0xf6,0xe,
  0x42,0xcd,0xa9,0x27,0xc,0xbd,0xd7,0xfb,0x91,0x40,0x40,0x66,0x2c,0x39,0x67,0x82,
  0xa6,0x3a,0x2d,0xe4,0x66,0x49,0xd3,0x81,0x50,0x4,0xf2,0xe9,0x6f,0x93,0x96,0x72,
  0x3f,0xe0,0xd1,0xd9,0x4e,0xb9,0x9f,0xb7,0x6,0x99,0xe6,0x8c,0xf0,0x6b,0xf2,0x2f,
  0xee,0x77,0xee,0x77,
    // play.svg
  0x0,0x0,0x0,0xf8,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x38,0x20,0x35,0x56,0x31,0x39,0x4c,0x31,0x39,0x20,0x31,0x32,0x4c,0x38,
  0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,
  0x66,0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,
  0x68,0x3d,0x22,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,
  0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // pause.svg
  0x0,0x0,0x1,0x5,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x36,0x22,0x20,0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,
  0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,
  0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,0x22,0x31,0x34,0x22,0x20,
  0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x34,0x22,0x20,
  0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,
  0x76,0x67,0x3e,0xa,
    // previous.svg
  0x0,0x0,0x0,0xfa,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x32,0x30,0x20,0x35,0x56,0x31,0x39,0x4c,0x39,0x20,0x31,0x32,0x4c,0x32,
  0x30,0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,
  0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,
  0x3d,0x22,0x34,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x33,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x34,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // volume.svg
  0x0,0x0,0x1,0xe4,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x39,0x2e,0x30,0x37,0x20,0x34,
  0x2e,0x39,0x33,0x43,0x32,0x30,0x2e,0x39,0x20,0x36,0x2e,0x37,0x36,0x20,0x32,0x31,
  0x2e,0x39,0x20,0x39,0x2e,0x33,0x20,0x32,0x31,0x2e,0x39,0x20,0x31,0x32,0x43,0x32,
  0x31,0x2e,0x39,0x20,0x31,0x34,0x2e,0x37,0x20,0x32,0x30,0x2e,0x39,0x20,0x31,0x37,
  0x2e,0x32,0x34,0x20,0x31,0x39,0x2e,0x30,0x37,0x20,0x31,0x39,0x2e,0x30,0x37,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,
  0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x35,0x2e,0x35,0x34,0x20,0x38,0x2e,
  0x34,0x36,0x43,0x31,0x36,0x2e,0x34,0x20,0x39,0x2e,0x33,0x32,0x20,0x31,0x36,0x2e,
  0x39,0x20,0x31,0x30,0x2e,0x36,0x20,0x31,0x36,0x2e,0x39,0x20,0x31,0x32,0x43,0x31,
  0x36,0x2e,0x39,0x20,0x31,0x33,0x2e,0x34,0x20,0x31,0x36,0x2e,0x34,0x20,0x31,0x34,
  0x2e,0x36,0x38,0x20,0x31,0x35,0x2e,0x35,0x34,0x20,0x31,0x35,0x2e,0x35,0x34,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // next.svg
  0x0,0x0,0x0,0xfa,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x34,0x20,0x35,0x56,0x31,0x39,0x4c,0x31,0x35,0x20,0x31,0x32,0x4c,0x34,
  0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x31,0x37,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x33,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x34,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // default_album_art.svg
  0x0,0x0,0x4,0x97,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x30,0x30,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,
  0x30,0x30,0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,
  0x20,0x32,0x30,0x30,0x20,0x32,0x30,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x6e,0x6f,0x6e,0x65,0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,
  0x70,0x3a,0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,
  0x30,0x30,0x30,0x2f,0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,
  0x20,0x42,0x61,0x63,0x6b,0x67,0x72,0x6f,0x75,0x6e,0x64,0x20,0x2d,0x2d,0x3e,0xa,
  0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x30,0x30,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x30,0x30,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x34,0x30,0x34,0x30,0x34,0x30,0x22,0x20,
  0x72,0x78,0x3d,0x22,0x38,0x22,0x2f,0x3e,0xa,0x20,0x20,0xa,0x20,0x20,0x3c,0x21,
  0x2d,0x2d,0x20,0x56,0x69,0x6e,0x79,0x6c,0x20,0x72,0x65,0x63,0x6f,0x72,0x64,0x20,
  0x64,0x65,0x73,0x69,0x67,0x6e,0x20,0x2d,0x2d,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,
  0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x38,0x30,0x22,0x20,0x66,0x69,
  0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,
  0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,
  0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x37,0x30,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x31,0x61,0x31,0x61,0x31,0x61,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,
  0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,
  0x3d,0x22,0x36,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,
  0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,
  0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,
  0x30,0x22,0x20,0x72,0x3d,0x22,0x35,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x23,0x31,0x61,0x31,0x61,0x31,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,
  0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x34,0x30,0x22,0x20,0x66,0x69,
  0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,
  0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,
  0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x33,0x30,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x31,0x61,0x31,0x61,0x31,0x61,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,
  0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,
  0x3d,0x22,0x32,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,
  0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,
  0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,
  0x30,0x22,0x20,0x72,0x3d,0x22,0x31,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x23,0x34,0x30,0x34,0x30,0x34,0x30,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,
  0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x34,0x22,0x20,0x66,0x69,0x6c,
  0x6c,0x3d,0x22,0x23,0x36,0x36,0x36,0x36,0x36,0x36,0x22,0x2f,0x3e,0xa,0x20,0x20,
  0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0x4d,0x75,0x73,0x69,0x63,0x20,0x6e,0x6f,
  0x74,0x65,0x20,0x69,0x63,0x6f,0x6e,0x20,0x2d,0x2d,0x3e,0xa,0x20,0x20,0x3c,0x67,
  0x20,0x74,0x72,0x61,0x6e,0x73,0x66,0x6f,0x72,0x6d,0x3d,0x22,0x74,0x72,0x61,0x6e,
  0x73,0x6c,0x61,0x74,0x65,0x28,0x38,0x35,0x2c,0x20,0x37,0x30,0x29,0x22,0x3e,0xa,
  0x20,0x20,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x38,0x20,
  0x31,0x38,0x56,0x36,0x4c,0x32,0x30,0x20,0x34,0x56,0x31,0x36,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x38,0x38,0x38,0x38,0x38,0x38,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,
  0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,
  0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,
  0x20,0x63,0x78,0x3d,0x22,0x36,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x38,0x22,0x20,
  0x72,0x3d,0x22,0x32,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x38,0x38,0x38,
  0x38,0x38,0x38,0x22,0x2f,0x3e,0xa,0x20,0x20,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,
  0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x38,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,
  0x36,0x22,0x20,0x72,0x3d,0x22,0x32,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,
  0x38,0x38,0x38,0x38,0x38,0x38,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x2f,0x67,0x3e,
  0xa,0x20,0x20,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0x44,0x65,0x63,0x6f,0x72,
  0x61,0x74,0x69,0x76,0x65,0x20,0x74,0x65,0x78,0x74,0x20,0x2d,0x2d,0x3e,0xa,0x20,
  0x20,0x3c,0x74,0x65,0x78,0x74,0x20,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x79,
  0x3d,0x22,0x31,0x36,0x30,0x22,0x20,0x74,0x65,0x78,0x74,0x2d,0x61,0x6e,0x63,0x68,
  0x6f,0x72,0x3d,0x22,0x6d,0x69,0x64,0x64,0x6c,0x65,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x36,0x36,0x36,0x36,0x36,0x36,0x22,0x20,0x66,0x6f,0x6e,0x74,0x2d,
  0x66,0x61,0x6d,0x69,0x6c,0x79,0x3d,0x22,0x41,0x72,0x69,0x61,0x6c,0x2c,0x20,0x73,
  0x61,0x6e,0x73,0x2d,0x73,0x65,0x72,0x69,0x66,0x22,0x20,0x66,0x6f,0x6e,0x74,0x2d,
  0x73,0x69,0x7a,0x65,0x3d,0x22,0x31,0x32,0x22,0x20,0x66,0x6f,0x6e,0x74,0x2d,0x77,
  0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x62,0x6f,0x6c,0x64,0x22,0x3e,0x4e,0x4f,0x20,
  0x41,0x52,0x54,0x57,0x4f,0x52,0x4b,0x3c,0x2f,0x74,0x65,0x78,0x74,0x3e,0xa,0x3c,
  0x2f,0x73,0x76,0x67,0x3e,0xa,
    // add.svg
  0x0,0x0,0x0,0xe6,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x32,0x20,0x35,0x56,0x31,0x39,0x4d,0x35,0x20,0x31,0x32,0x48,0x31,
  0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,
  0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,0xa,
    // app_icon.svg
  0x0,0x0,0x0,0xf4,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x34,0x38,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x34,0x38,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x34,
  0x38,0x20,0x34,0x38,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,
  0x63,0x78,0x3d,0x22,0x32,0x34,0x22,0x20,0x63,0x79,0x3d,0x22,0x32,0x34,0x22,0x20,
  0x72,0x3d,0x22,0x32,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x32,0x31,
  0x39,0x36,0x46,0x33,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,
  0x64,0x3d,0x22,0x4d,0x31,0x38,0x20,0x31,0x34,0x56,0x33,0x34,0x4c,0x33,0x34,0x20,
  0x32,0x34,0x4c,0x31,0x38,0x20,0x31,0x34,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,
  0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // volume_mute.svg
  0x0,0x0,0x1,0x23,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x32,0x33,0x20,0x39,0x4c,0x31,0x37,
  0x20,0x31,0x35,0x4d,0x31,0x37,0x20,0x39,0x4c,0x32,0x33,0x20,0x31,0x35,0x22,0x20,
  0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,
  0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,
  0x3e,0xa,
    // remove.svg
  0x0,0x0,0x0,0xde,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x35,0x20,0x31,0x32,0x48,0x31,0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,
  0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // stop.svg
  0x0,0x0,0x0,0xca,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x36,0x22,0x20,0x79,0x3d,0x22,0x36,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x31,0x32,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x32,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // styles
  0x0,0x6,
  0x7,0xac,0x2,0xc3,
  0x0,0x73,
  0x0,0x74,0x0,0x79,0x0,0x6c,0x0,0x65,0x0,0x73,
    // dark_theme.qss
  0x0,0xe,
  0xd,0x16,0x97,0xc3,
  0x0,0x64,
  0x0,0x61,0x0,0x72,0x0,0x6b,0x0,0x5f,0x0,0x74,0x0,0x68,0x0,0x65,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x71,0x0,0x73,0x0,0x73,
    // play.svg
  0x0,0x8,
  0x2,0x8c,0x54,0x27,
  0x0,0x70,
  0x0,0x6c,0x0,0x61,0x0,0x79,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // pause.svg
  0x0,0x9,
  0xc,0x98,0xb7,0xc7,
  0x0,0x70,
  0x0,0x61,0x0,0x75,0x0,0x73,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // previous.svg
  0x0,0xc,
  0x8,0x37,0xc0,0xc7,
  0x0,0x70,
  0x0,0x72,0x0,0x65,0x0,0x76,0x0,0x69,0x0,0x6f,0x0,0x75,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume.svg
  0x0,0xa,
  0xc,0x3b,0xf6,0xa7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // next.svg
  0x0,0x8,
  0xc,0xf7,0x54,0x47,
  0x0,0x6e,
  0x0,0x65,0x0,0x78,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // default_album_art.svg
  0x0,0x15,
  0xa,0xf6,0x60,0xa7,
  0x0,0x64,
  0x0,0x65,0x0,0x66,0x0,0x61,0x0,0x75,0x0,0x6c,0x0,0x74,0x0,0x5f,0x0,0x61,0x0,0x6c,0x0,0x62,0x0,0x75,0x0,0x6d,0x0,0x5f,0x0,0x61,0x0,0x72,0x0,0x74,
  0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // add.svg
  0x0,0x7,
  0x7,0xa7,0x5a,0x7,
  0x0,0x61,
  0x0,0x64,0x0,0x64,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // app_icon.svg
  0x0,0xc,
  0x7,0x6f,0x91,0x27,
  0x0,0x61,
  0x0,0x70,0x0,0x70,0x0,0x5f,0x0,0x69,0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume_mute.svg
  0x0,0xf,
  0xb,0x22,0xfb,0xc7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x5f,0x0,0x6d,0x0,0x75,0x0,0x74,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // remove.svg
  0x0,0xa,
  0x6,0xcb,0x42,0x47,
  0x0,0x72,
  0x0,0x65,0x0,0x6d,0x0,0x6f,0x0,0x76,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // stop.svg
  0x0,0x8,
  0xb,0x63,0x55,0x87,
  0x0,0x73,
  0x0,0x74,0x0,0x6f,0x0,0x70,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0xb,0x0,0x0,0x0,0x4,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles
  0x0,0x0,0x0,0x10,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles/dark_theme.qss
  0x0,0x0,0x0,0x22,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xd9,0x9f,0x3b,0xfa,
  // :/icons/play.svg
  0x0,0x0,0x0,0x44,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x4,0xf9,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x6a,0xb1,
  // :/icons/remove.svg
  0x0,0x0,0x1,0x46,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x12,0x86,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x1a,0x8,
  // :/icons/app_icon.svg
  0x0,0x0,0x1,0x4,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x10,0x67,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x2f,0xeb,
  // :/icons/add.svg
  0x0,0x0,0x0,0xf0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xf,0x7d,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x5,0xd4,
  // :/icons/previous.svg
  0x0,0x0,0x0,0x72,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x6,0xfe,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xbb,0x98,
  // :/icons/default_album_art.svg
  0x0,0x0,0x0,0xc0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xa,0xe2,
0x0,0x0,0x1,0x97,0xd9,0x9d,0x64,0x35,
  // :/icons/volume_mute.svg
  0x0,0x0,0x1,0x22,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x11,0x5f,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xf2,0x4e,
  // :/icons/stop.svg
  0x0,0x0,0x1,0x60,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x13,0x68,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x93,0x2b,
  // :/icons/volume.svg
  0x0,0x0,0x0,0x90,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x7,0xfc,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xdb,0xe5,
  // :/icons/pause.svg
  0x0,0x0,0x0,0x5a,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x5,0xf5,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x7f,0x6f,
  // :/icons/next.svg
  0x0,0x0,0x0,0xaa,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x9,0xe4,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xa7,0x9c,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
