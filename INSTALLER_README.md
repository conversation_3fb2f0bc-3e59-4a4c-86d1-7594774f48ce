# Audio Player Installer

This directory contains everything needed to create a professional Windows installer for the Audio Player application using NSIS (Nullsoft Scriptable Install System).

## 📦 Installer Features

### ✨ **What the Installer Includes:**
- **Complete Application** with all Qt dependencies
- **Desktop Shortcut** for easy access
- **Start Menu Shortcuts** (Application + Uninstaller)
- **File Associations** for common audio formats (MP3, WAV, FLAC, OGG, M4A, AAC)
- **Uninstaller** with complete cleanup
- **Registry Entries** for Add/Remove Programs
- **Professional UI** with modern NSIS interface

### 🎯 **Installer Components:**
- **Main Application** (required) - AudioPlayer.exe and Qt libraries
- **Desktop Shortcut** (optional) - Creates desktop shortcut
- **Start Menu Shortcuts** (optional) - Creates start menu folder
- **File Associations** (optional) - Associates audio files with Audio Player

## 🛠️ Prerequisites

### Required Software:
1. **NSIS (Nullsoft Scriptable Install System)**
   - Download from: https://nsis.sourceforge.io/
   - Install to default location: `C:\Program Files (x86)\NSIS`

2. **Qt 6.9.1 with MinGW**
   - Should be installed at: `C:\Qt\6.9.1\mingw_64`
   - MinGW tools at: `C:\Qt\Tools\mingw1310_64`

3. **Built Application**
   - AudioPlayer.exe must exist in `build\release\`

## 🚀 Building the Installer

### Method 1: Complete Build + Installer (Recommended)
```batch
build_installer.bat
```
This script will:
1. Clean previous builds
2. Build the application from source
3. Deploy Qt dependencies
4. Create the installer

### Method 2: Auto-Deploy Build + Installer
```batch
build_installer_auto.bat
```
This script uses `windeployqt` to automatically gather all dependencies.

### Method 3: Installer Only (if app is already built)
```batch
make_installer_only.bat
```
Use this if you've already built the application and just want to create the installer.

## 📁 Files Included

### Core Files:
- `installer.nsi` - Main NSIS installer script
- `LICENSE.txt` - License agreement shown during installation
- `build_installer.bat` - Complete build and installer creation
- `build_installer_auto.bat` - Auto-deploy version
- `make_installer_only.bat` - Installer creation only

### Generated Files:
- `AudioPlayer_Setup.exe` - Final installer (created after running scripts)

## ⚙️ Customizing the Installer

### Paths Configuration:
Edit the batch scripts to update paths if your Qt installation is different:
```batch
set QT_DIR=C:\Qt\6.9.1\mingw_64
set MINGW_DIR=C:\Qt\Tools\mingw1310_64
set NSIS_DIR=C:\Program Files (x86)\NSIS
```

### Installer Script Customization:
Edit `installer.nsi` to customize:
- Application name and version
- Installation directory
- File associations
- Registry entries
- Shortcuts and icons

## 🔧 Troubleshooting

### Common Issues:

**"NSIS not found"**
- Install NSIS from https://nsis.sourceforge.io/
- Update `NSIS_DIR` path in batch scripts

**"Qt not found"**
- Ensure Qt 6.9.1 is installed
- Update `QT_DIR` path in batch scripts

**"AudioPlayer.exe not found"**
- Build the application first using Qt Creator or qmake
- Ensure executable exists in `build\release\`

**"DLL not found during installer build"**
- Check that all Qt DLL paths in `installer.nsi` are correct
- Use `build_installer_auto.bat` for automatic dependency detection

**"Installer won't run on target machine"**
- Ensure all required Visual C++ redistributables are installed
- Consider including redistributables in the installer

## 📋 Installer Details

### Installation Process:
1. **Welcome Screen** - Greets the user
2. **License Agreement** - Shows LICENSE.txt
3. **Component Selection** - Choose what to install
4. **Directory Selection** - Choose installation folder
5. **Start Menu Configuration** - Configure start menu folder
6. **Installation** - Copies files and creates shortcuts
7. **Completion** - Installation finished

### What Gets Installed:
- Main application executable
- All required Qt 6 DLLs
- Qt plugins (platforms, styles, imageformats, multimedia)
- MinGW runtime libraries
- Desktop and Start Menu shortcuts
- Registry entries for uninstallation
- File associations for audio formats

### Uninstallation:
- Complete removal of all installed files
- Cleanup of registry entries
- Removal of shortcuts
- Removal of file associations

## 🎵 Distribution

The final `AudioPlayer_Setup.exe` is a standalone installer that can be distributed to end users. It includes everything needed to run the Audio Player on Windows systems.

### Installer Size:
Typically 15-25 MB including all Qt dependencies.

### System Requirements:
- Windows 10 or later (64-bit)
- ~50 MB disk space
- Administrator privileges for installation
