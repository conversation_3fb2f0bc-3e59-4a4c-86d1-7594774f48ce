#ifndef AUDIOPLAYER_H
#define AUDIOPLAYER_H

#include <QObject>
#include <QMediaPlayer>
#include <QAudioOutput>
#include <QUrl>
#include <QTimer>

class AudioPlayer : public QObject
{
    Q_OBJECT

public:
    enum PlaybackState {
        StoppedState,
        PlayingState,
        PausedState
    };

    explicit AudioPlayer(QObject *parent = nullptr);
    ~AudioPlayer();

    // Playback control
    void play();
    void pause();
    void stop();
    void setSource(const QUrl &url);
    
    // Position and duration
    qint64 position() const;
    qint64 duration() const;
    void setPosition(qint64 position);
    
    // Volume control
    float volume() const;
    void setVolume(float volume);
    bool isMuted() const;
    void setMuted(bool muted);
    
    // State
    PlaybackState playbackState() const;
    QMediaPlayer::MediaStatus mediaStatus() const;
    
    // Error handling
    QString errorString() const;

signals:
    void positionChanged(qint64 position);
    void durationChanged(qint64 duration);
    void playbackStateChanged(AudioPlayer::PlaybackState state);
    void volumeChanged(float volume);
    void mutedChanged(bool muted);
    void mediaStatusChanged(QMediaPlayer::MediaStatus status);
    void errorOccurred(const QString &error);

private slots:
    void onMediaStatusChanged(QMediaPlayer::MediaStatus status);
    void onPlaybackStateChanged(QMediaPlayer::PlaybackState state);
    void onErrorOccurred(QMediaPlayer::Error error, const QString &errorString);

private:
    QMediaPlayer *m_mediaPlayer;
    QAudioOutput *m_audioOutput;
    PlaybackState m_playbackState;
    
    void updatePlaybackState();
};

#endif // AUDIOPLAYER_H
