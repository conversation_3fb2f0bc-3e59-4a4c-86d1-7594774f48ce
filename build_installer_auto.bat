@echo off
echo ========================================
echo Audio Player Installer Builder (Auto)
echo Using windeployqt for dependencies
echo ========================================
echo.

REM Set paths
set QT_DIR=C:\Qt\6.9.1\mingw_64
set MINGW_DIR=C:\Qt\Tools\mingw1310_64
set NSIS_DIR=C:\Program Files (x86)\NSIS
set PROJECT_DIR=%~dp0

REM Add Qt and MinGW to PATH
set PATH=%PATH%;%QT_DIR%\bin;%MINGW_DIR%\bin

echo [1/7] Checking prerequisites...

REM Check if NSIS is installed
if not exist "%NSIS_DIR%\makensis.exe" (
    echo ERROR: NSIS not found at "%NSIS_DIR%"
    echo Please install NSIS from https://nsis.sourceforge.io/
    pause
    exit /b 1
)

echo Prerequisites check passed!
echo.

echo [2/7] Cleaning previous build...
if exist "build" rmdir /s /q "build"
if exist "debug" rmdir /s /q "debug"
if exist "release" rmdir /s /q "release"
if exist "Makefile*" del /q "Makefile*"
if exist "installer_temp" rmdir /s /q "installer_temp"
if exist "AudioPlayer_Setup.exe" del /q "AudioPlayer_Setup.exe"

echo [3/7] Building application...
echo Running qmake...
qmake AudioPlayer.pro
if errorlevel 1 (
    echo ERROR: qmake failed
    pause
    exit /b 1
)

echo Running make...
mingw32-make release
if errorlevel 1 (
    echo ERROR: make failed
    pause
    exit /b 1
)

echo [4/7] Checking if executable was built...
if not exist "build\release\AudioPlayer.exe" (
    echo ERROR: AudioPlayer.exe not found in build\release\
    pause
    exit /b 1
)

echo [5/7] Creating deployment directory...
mkdir installer_temp
copy "build\release\AudioPlayer.exe" "installer_temp\"

echo [6/7] Deploying Qt dependencies...
echo Running windeployqt...
"%QT_DIR%\bin\windeployqt.exe" --release --no-translations --no-system-d3d-compiler --no-opengl-sw "installer_temp\AudioPlayer.exe"
if errorlevel 1 (
    echo ERROR: windeployqt failed
    pause
    exit /b 1
)

echo [7/7] Building installer with NSIS...
echo Creating installer script for auto-deployment...

REM Create a temporary NSIS script that uses the deployed files
echo ;Auto-generated installer script > installer_auto.nsi
echo !include "MUI2.nsh" >> installer_auto.nsi
echo !include "FileFunc.nsh" >> installer_auto.nsi
echo. >> installer_auto.nsi
echo Name "Audio Player" >> installer_auto.nsi
echo OutFile "AudioPlayer_Setup.exe" >> installer_auto.nsi
echo Unicode True >> installer_auto.nsi
echo InstallDir "$PROGRAMFILES64\Audio Player" >> installer_auto.nsi
echo RequestExecutionLevel admin >> installer_auto.nsi
echo. >> installer_auto.nsi
echo !define MUI_ABORTWARNING >> installer_auto.nsi
echo. >> installer_auto.nsi
echo !insertmacro MUI_PAGE_WELCOME >> installer_auto.nsi
echo !insertmacro MUI_PAGE_LICENSE "LICENSE.txt" >> installer_auto.nsi
echo !insertmacro MUI_PAGE_DIRECTORY >> installer_auto.nsi
echo !insertmacro MUI_PAGE_INSTFILES >> installer_auto.nsi
echo !insertmacro MUI_PAGE_FINISH >> installer_auto.nsi
echo. >> installer_auto.nsi
echo !insertmacro MUI_UNPAGE_WELCOME >> installer_auto.nsi
echo !insertmacro MUI_UNPAGE_CONFIRM >> installer_auto.nsi
echo !insertmacro MUI_UNPAGE_INSTFILES >> installer_auto.nsi
echo. >> installer_auto.nsi
echo !insertmacro MUI_LANGUAGE "English" >> installer_auto.nsi
echo. >> installer_auto.nsi
echo Section "Audio Player" >> installer_auto.nsi
echo   SetOutPath $INSTDIR >> installer_auto.nsi
echo   File /r "installer_temp\*" >> installer_auto.nsi
echo   WriteUninstaller "$INSTDIR\Uninstall.exe" >> installer_auto.nsi
echo   CreateShortcut "$DESKTOP\Audio Player.lnk" "$INSTDIR\AudioPlayer.exe" >> installer_auto.nsi
echo   CreateDirectory "$SMPROGRAMS\Audio Player" >> installer_auto.nsi
echo   CreateShortcut "$SMPROGRAMS\Audio Player\Audio Player.lnk" "$INSTDIR\AudioPlayer.exe" >> installer_auto.nsi
echo   CreateShortcut "$SMPROGRAMS\Audio Player\Uninstall.lnk" "$INSTDIR\Uninstall.exe" >> installer_auto.nsi
echo   WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player" "DisplayName" "Audio Player" >> installer_auto.nsi
echo   WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player" "UninstallString" "$INSTDIR\Uninstall.exe" >> installer_auto.nsi
echo SectionEnd >> installer_auto.nsi
echo. >> installer_auto.nsi
echo Section "Uninstall" >> installer_auto.nsi
echo   RMDir /r "$INSTDIR" >> installer_auto.nsi
echo   Delete "$DESKTOP\Audio Player.lnk" >> installer_auto.nsi
echo   RMDir /r "$SMPROGRAMS\Audio Player" >> installer_auto.nsi
echo   DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player" >> installer_auto.nsi
echo SectionEnd >> installer_auto.nsi

echo Running makensis...
"%NSIS_DIR%\makensis.exe" installer_auto.nsi
if errorlevel 1 (
    echo ERROR: NSIS installer build failed
    pause
    exit /b 1
)

echo Cleaning up temporary files...
rmdir /s /q "installer_temp"
del "installer_auto.nsi"

echo.
echo ========================================
echo SUCCESS! Installer built successfully!
echo ========================================
echo.
echo Installer file: AudioPlayer_Setup.exe
echo Size: 
for %%A in (AudioPlayer_Setup.exe) do echo %%~zA bytes

echo.
echo The installer includes all necessary Qt dependencies
echo and will create desktop and start menu shortcuts.
echo.
pause
