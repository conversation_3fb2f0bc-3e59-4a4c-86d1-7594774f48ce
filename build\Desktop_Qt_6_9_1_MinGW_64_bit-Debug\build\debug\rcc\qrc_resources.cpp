/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.9.1
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // dark_theme.qss
  0x0,0x0,0x4,0xbe,
  0x0,
  0x0,0x15,0x55,0x78,0xda,0xb5,0x57,0x5b,0x4f,0xe3,0x38,0x14,0x7e,0xef,0xaf,0xb0,
  0x40,0x2b,0x4d,0x51,0xb3,0x24,0xa1,0x29,0x10,0x9e,0xca,0x30,0x48,0x2b,0xd,0x12,
  0x23,0xba,0x3b,0xcf,0x6e,0x62,0x5a,0xb,0x37,0xce,0x3a,0xce,0x50,0x66,0x35,0xff,
  0x7d,0x7d,0x89,0x73,0x69,0x9c,0x4b,0x59,0xd6,0x91,0xaa,0xd8,0x27,0x3d,0x3e,0x97,
  0xef,0x1c,0x7f,0x3e,0x3f,0x3,0x77,0x90,0xbd,0x80,0xd5,0x16,0xed,0x10,0x78,0xe2,
  0x6f,0x4,0x65,0x5b,0x84,0x38,0x78,0xa6,0xc,0x2c,0xf3,0x18,0x53,0xf0,0x48,0xe0,
  0x1b,0x62,0xe0,0xec,0x7c,0x32,0x39,0x3f,0x3,0xf,0x10,0x27,0xe0,0x3b,0x4e,0x62,
  0xfa,0x2a,0x97,0xbe,0xc9,0x79,0x31,0xfd,0x67,0x2,0xc4,0x58,0xc3,0xe8,0x65,0xc3,
  0x68,0x9e,0xc4,0x4e,0x44,0x9,0x65,0x21,0x38,0xf5,0xd7,0xf2,0xb9,0x51,0x62,0xb3,
  0xf6,0xac,0xc6,0xcd,0xe4,0xd7,0x64,0xf2,0xed,0x3b,0x8e,0x37,0x62,0xcb,0x77,0xfd,
  0x5f,0xae,0x3d,0xd3,0x84,0x3b,0xcf,0x70,0x87,0xc9,0x5b,0x8,0x4e,0x9e,0xd0,0x86,
  0x22,0xf0,0xe7,0x1f,0x27,0x33,0xb0,0x64,0x18,0x92,0x19,0xc8,0x60,0x92,0x39,0x19,
  0x62,0x58,0xef,0x27,0xbc,0xb8,0xcd,0x39,0xa7,0x49,0xa6,0x3c,0x78,0xcc,0xb3,0xad,
  0x9e,0x77,0x5b,0x30,0x77,0xe5,0xa3,0x77,0x5b,0x53,0x16,0x23,0xb1,0xe8,0xa5,0x7b,
  0x90,0x51,0x82,0x63,0x70,0x1a,0xa8,0x51,0x17,0x3b,0xc,0xc6,0x38,0xcf,0x42,0xb0,
  0x48,0xf7,0x7a,0x3d,0x85,0x71,0x8c,0x93,0x4d,0x8,0xae,0xc4,0xff,0xbc,0x72,0x79,
  0x87,0x13,0xe7,0x15,0xc7,0x7c,0x2b,0x4,0xae,0x59,0x54,0xe,0xbd,0x22,0xbc,0xd9,
  0xf2,0x10,0x4,0xae,0xab,0xc3,0x54,0x19,0x1a,0x6e,0xe9,0xf,0x91,0x92,0x4e,0x73,
  0x3,0x57,0x3e,0xd,0x7b,0x8c,0xe8,0x52,0x8d,0x96,0xc2,0x94,0xa1,0x2c,0x43,0x71,
  0xb7,0xca,0x8b,0x40,0x3e,0x56,0x95,0x17,0x6a,0xb4,0x54,0xc6,0x38,0x83,0x6b,0xd2,
  0xa7,0xd3,0x87,0xf2,0xb1,0xeb,0x84,0xf2,0x69,0xa6,0x7c,0xa1,0x86,0x49,0xe1,0x67,
  0x11,0x23,0x46,0x49,0x99,0xca,0x4f,0x12,0xa7,0x33,0xf0,0x8,0xf3,0xc,0xcd,0x0,
  0xe2,0xd1,0xef,0xd3,0x83,0xf4,0x9e,0xa6,0xe2,0xb,0x25,0xd7,0xf3,0x59,0x43,0x98,
  0x71,0x9a,0xda,0xd6,0x13,0xb4,0xe7,0xb6,0x75,0x11,0xb1,0x1f,0x98,0xe6,0x99,0x4d,
  0xb6,0xcb,0x39,0x7a,0xf,0xa4,0xfc,0x51,0x90,0xa,0xdc,0xdf,0xda,0x90,0x3a,0xc,
  0xff,0xa1,0xb3,0x1a,0x32,0x5d,0x2e,0xdb,0xa4,0x95,0xe3,0x36,0x69,0xd3,0x7d,0xdb,
  0x17,0x55,0x10,0x86,0xe0,0xea,0x7b,0xd7,0x8b,0xfb,0xb,0x2b,0xe,0xbc,0xeb,0xcb,
  0xc5,0x9d,0x3f,0xe8,0x5c,0x1,0xdf,0x4e,0xf7,0xac,0xf2,0x9a,0x83,0x56,0xf9,0x81,
  0x8b,0xd6,0x6f,0x6a,0x4e,0xe,0x96,0x90,0x17,0x2c,0x82,0xcf,0xf6,0xaa,0x74,0xef,
  0xe6,0x97,0x4b,0xcf,0x40,0xfb,0x49,0x20,0x0,0x31,0xdd,0x9d,0xf4,0x7b,0x18,0xa,
  0x5d,0x22,0x88,0x22,0x92,0xc,0xff,0x14,0xc8,0x87,0xc4,0xec,0xd3,0xee,0x46,0xd7,
  0x6a,0xe8,0x7d,0xb6,0x45,0x13,0xb9,0x32,0xad,0xa5,0xb2,0x2b,0x4,0x7f,0x13,0x9c,
  0x20,0xc8,0x36,0x12,0x58,0x28,0xe1,0x9f,0xf6,0x5e,0xe8,0xce,0xc0,0x9b,0xfa,0xdd,
  0xfb,0xea,0xdd,0xf,0x3d,0xd1,0x3f,0x45,0x20,0x43,0xd7,0x40,0xb6,0x98,0x7b,0xa6,
  0x27,0x4c,0x8b,0x46,0x26,0x14,0xe1,0x44,0x63,0xd8,0xb5,0xe2,0x76,0x6e,0x50,0x6a,
  0x7c,0xda,0xc2,0x24,0x26,0x36,0x9f,0xfe,0x8b,0x8d,0xfe,0x32,0xb8,0xf,0x2a,0x1b,
  0x35,0xb6,0xa6,0x9d,0xad,0xdb,0x0,0x4c,0x8a,0x8b,0x3e,0xec,0x95,0xc1,0x32,0x3e,
  0x39,0xdd,0x4e,0x5d,0xf,0x3a,0xd5,0x81,0xfe,0xe3,0x5d,0x5b,0xcc,0x6f,0x83,0xfb,
  0x45,0xe5,0x9a,0x76,0x75,0x6a,0xaf,0x9b,0x2f,0x57,0x57,0x5f,0x82,0x21,0xcb,0xba,
  0x20,0x7b,0xbc,0x6d,0x3a,0x8c,0x95,0x6d,0x1a,0xeb,0xd3,0x21,0xb0,0x97,0xb6,0x65,
  0xf9,0xda,0x49,0xe1,0xe6,0x83,0xc1,0xa0,0x93,0x5f,0xb3,0x4a,0x59,0xd9,0x3,0x86,
  0x5a,0x85,0xb6,0x2b,0xc7,0x8e,0x66,0x51,0xaf,0x5f,0x71,0xc6,0xc1,0x5f,0x18,0xbd,
  0xea,0x43,0x88,0x88,0xa9,0x3e,0x7b,0xa4,0x40,0xad,0x1f,0x71,0xac,0x1e,0x4d,0x2c,
  0x20,0xe1,0x88,0x25,0x90,0x23,0x67,0xe0,0x94,0xc9,0x10,0x41,0x11,0xc7,0x34,0x71,
  0x6,0x7a,0x30,0xcd,0xb9,0xc,0x74,0x8,0x12,0x9a,0x20,0x9d,0x27,0xe3,0x49,0x18,
  0x62,0x8e,0x76,0x85,0x3f,0xe5,0xf9,0xe3,0xb9,0x7,0x41,0x5a,0x53,0xd1,0x12,0x77,
  0xd,0x57,0xe6,0x6a,0x74,0x93,0xc0,0xe6,0xe,0xa1,0x36,0xb6,0x97,0x3d,0xd4,0x2c,
  0x1e,0xa1,0x70,0x24,0x65,0xea,0x31,0x65,0x48,0x45,0xad,0xe6,0x24,0x28,0xe0,0x1a,
  0x11,0xdd,0xc3,0xd5,0x6b,0xe7,0xff,0x38,0x13,0xf4,0x34,0x85,0x4c,0x0,0xba,0xc7,
  0x19,0xa9,0xe2,0x54,0x7c,0x1a,0xbd,0xac,0x30,0x27,0xa8,0xae,0x52,0xb1,0xc5,0xc,
  0xff,0x44,0xf5,0xd6,0xd5,0xa0,0x90,0x6b,0x4a,0xe2,0x51,0xba,0x97,0x8c,0xb,0xdf,
  0xbb,0x94,0xcf,0x8d,0x72,0xa3,0x25,0x52,0xa3,0xae,0x25,0x2d,0xf0,0x3f,0xa8,0x62,
  0xa4,0x7d,0xd,0x98,0x5,0xba,0xb,0x57,0xbb,0x45,0x39,0x93,0x61,0x5b,0xe1,0x9d,
  0xe,0xc8,0xac,0x74,0x86,0x8a,0x6,0x52,0x2e,0xd7,0xed,0x28,0xaf,0x9,0x82,0x3f,
  0xa,0x64,0xc2,0x4c,0x5c,0x13,0x4e,0x1e,0x68,0x2,0x23,0x2a,0xde,0x76,0x34,0xa1,
  0x22,0x17,0x11,0xba,0x69,0x59,0xee,0x77,0x39,0x7f,0xc0,0xe2,0xe7,0x41,0xd5,0x18,
  0x1e,0x50,0x92,0x83,0x5b,0xc8,0xf4,0x4d,0x49,0x4c,0xe4,0xfb,0x31,0x14,0xdb,0x52,
  0x47,0xf5,0x96,0xd0,0x1b,0x2d,0xdf,0x9c,0x4c,0xc5,0xc6,0x8d,0xd2,0x1d,0x1,0xc3,
  0x52,0xd1,0x42,0x5e,0x58,0xfc,0x81,0x36,0xd8,0xdc,0xe5,0x3,0xca,0xb7,0xa9,0x6f,
  0x98,0x5c,0x55,0x44,0xd1,0x4,0xde,0x4,0xfd,0x7f,0xed,0xbe,0x65,0x94,0x1a,0x81,
  0xb0,0xb7,0x49,0x79,0xf3,0xf3,0xe7,0x63,0x2,0xf9,0xa1,0x51,0x14,0xa7,0x2b,0x12,
  0x89,0x85,0x9c,0x1a,0xf0,0x99,0xb3,0xcd,0x6b,0xb3,0xc2,0xaa,0x21,0xd6,0x9c,0x37,
  0x3c,0x48,0xd8,0x58,0xde,0x35,0x24,0x4f,0xe5,0x90,0xe7,0x59,0x9,0x70,0x3d,0x3d,
  0x16,0xe2,0xf2,0x68,0x1e,0xc2,0x77,0xbd,0xd4,0x5a,0x1,0x97,0x86,0xa4,0x4,0x73,
  0x8e,0xa,0x33,0x8a,0x89,0x21,0x3c,0x3d,0x3d,0xbf,0xd8,0x4a,0x11,0x91,0x83,0x7f,
  0xb5,0x89,0x48,0x51,0xdf,0x17,0x1d,0xf9,0xf3,0x4a,0x22,0xd8,0xd6,0xd4,0x7b,0x6c,
  0xd4,0x6e,0xe4,0xd2,0x95,0x48,0x5c,0x6a,0x89,0x8c,0x69,0xc1,0xff,0xd5,0x5c,0x16,
  0x82,0x50,0xc2,0x71,0x4,0xc9,0xb8,0x2b,0xa5,0xe1,0xb2,0x5d,0x75,0xbb,0x28,0xcd,
  0x2d,0x37,0x30,0xf6,0xe,0x6f,0x64,0x6e,0xe2,0x7d,0x85,0x21,0x5b,0xa2,0xc1,0x99,
  0xef,0xe,0xef,0x76,0x44,0x98,0xea,0x5a,0x4,0x1a,0x1c,0x45,0x56,0x8c,0x9e,0x59,
  0x43,0x2c,0x79,0x65,0x43,0x7c,0x70,0x6d,0xd2,0xc,0xe7,0x90,0x67,0x56,0xbc,0xa7,
  0x52,0xd5,0x43,0x4c,0xad,0x9,0x28,0x6b,0xec,0x1d,0x19,0x18,0xb3,0xd7,0xd8,0x1c,
  0x14,0x40,0xe8,0x4b,0xc1,0xe0,0x5d,0x65,0x74,0x12,0x2a,0x4d,0x1d,0x69,0xe8,0xbc,
  0xbf,0xe,0x24,0x42,0x54,0xc6,0x8a,0x52,0xc2,0x71,0xaa,0xcb,0x42,0x4e,0x56,0x38,
  0xed,0x49,0x45,0x20,0x9f,0xee,0x63,0xd2,0xd2,0xee,0x7b,0x22,0x3a,0x6f,0xb5,0xfb,
  0x45,0x83,0xc8,0xd4,0x39,0xc2,0xaf,0xc9,0xbf,0x34,0xf2,0x99,0x55,
    // play.svg
  0x0,0x0,0x0,0xf8,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x38,0x20,0x35,0x56,0x31,0x39,0x4c,0x31,0x39,0x20,0x31,0x32,0x4c,0x38,
  0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,
  0x66,0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,
  0x68,0x3d,0x22,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,
  0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // pause.svg
  0x0,0x0,0x1,0x5,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x36,0x22,0x20,0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,
  0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,
  0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,0x22,0x31,0x34,0x22,0x20,
  0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x34,0x22,0x20,
  0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,
  0x76,0x67,0x3e,0xa,
    // previous.svg
  0x0,0x0,0x0,0xfa,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x32,0x30,0x20,0x35,0x56,0x31,0x39,0x4c,0x39,0x20,0x31,0x32,0x4c,0x32,
  0x30,0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,
  0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,
  0x3d,0x22,0x34,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x33,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x34,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // volume.svg
  0x0,0x0,0x1,0xe4,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x39,0x2e,0x30,0x37,0x20,0x34,
  0x2e,0x39,0x33,0x43,0x32,0x30,0x2e,0x39,0x20,0x36,0x2e,0x37,0x36,0x20,0x32,0x31,
  0x2e,0x39,0x20,0x39,0x2e,0x33,0x20,0x32,0x31,0x2e,0x39,0x20,0x31,0x32,0x43,0x32,
  0x31,0x2e,0x39,0x20,0x31,0x34,0x2e,0x37,0x20,0x32,0x30,0x2e,0x39,0x20,0x31,0x37,
  0x2e,0x32,0x34,0x20,0x31,0x39,0x2e,0x30,0x37,0x20,0x31,0x39,0x2e,0x30,0x37,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,
  0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x35,0x2e,0x35,0x34,0x20,0x38,0x2e,
  0x34,0x36,0x43,0x31,0x36,0x2e,0x34,0x20,0x39,0x2e,0x33,0x32,0x20,0x31,0x36,0x2e,
  0x39,0x20,0x31,0x30,0x2e,0x36,0x20,0x31,0x36,0x2e,0x39,0x20,0x31,0x32,0x43,0x31,
  0x36,0x2e,0x39,0x20,0x31,0x33,0x2e,0x34,0x20,0x31,0x36,0x2e,0x34,0x20,0x31,0x34,
  0x2e,0x36,0x38,0x20,0x31,0x35,0x2e,0x35,0x34,0x20,0x31,0x35,0x2e,0x35,0x34,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // next.svg
  0x0,0x0,0x0,0xfa,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x34,0x20,0x35,0x56,0x31,0x39,0x4c,0x31,0x35,0x20,0x31,0x32,0x4c,0x34,
  0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x31,0x37,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x33,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x34,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // add.svg
  0x0,0x0,0x0,0xe6,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x32,0x20,0x35,0x56,0x31,0x39,0x4d,0x35,0x20,0x31,0x32,0x48,0x31,
  0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,
  0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,0xa,
    // app_icon.svg
  0x0,0x0,0x0,0xf4,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x34,0x38,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x34,0x38,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x34,
  0x38,0x20,0x34,0x38,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,
  0x63,0x78,0x3d,0x22,0x32,0x34,0x22,0x20,0x63,0x79,0x3d,0x22,0x32,0x34,0x22,0x20,
  0x72,0x3d,0x22,0x32,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x32,0x31,
  0x39,0x36,0x46,0x33,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,
  0x64,0x3d,0x22,0x4d,0x31,0x38,0x20,0x31,0x34,0x56,0x33,0x34,0x4c,0x33,0x34,0x20,
  0x32,0x34,0x4c,0x31,0x38,0x20,0x31,0x34,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,
  0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // volume_mute.svg
  0x0,0x0,0x1,0x23,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x32,0x33,0x20,0x39,0x4c,0x31,0x37,
  0x20,0x31,0x35,0x4d,0x31,0x37,0x20,0x39,0x4c,0x32,0x33,0x20,0x31,0x35,0x22,0x20,
  0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,
  0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,
  0x3e,0xa,
    // remove.svg
  0x0,0x0,0x0,0xde,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x35,0x20,0x31,0x32,0x48,0x31,0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,
  0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // stop.svg
  0x0,0x0,0x0,0xca,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x36,0x22,0x20,0x79,0x3d,0x22,0x36,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x31,0x32,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x32,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // styles
  0x0,0x6,
  0x7,0xac,0x2,0xc3,
  0x0,0x73,
  0x0,0x74,0x0,0x79,0x0,0x6c,0x0,0x65,0x0,0x73,
    // dark_theme.qss
  0x0,0xe,
  0xd,0x16,0x97,0xc3,
  0x0,0x64,
  0x0,0x61,0x0,0x72,0x0,0x6b,0x0,0x5f,0x0,0x74,0x0,0x68,0x0,0x65,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x71,0x0,0x73,0x0,0x73,
    // play.svg
  0x0,0x8,
  0x2,0x8c,0x54,0x27,
  0x0,0x70,
  0x0,0x6c,0x0,0x61,0x0,0x79,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // pause.svg
  0x0,0x9,
  0xc,0x98,0xb7,0xc7,
  0x0,0x70,
  0x0,0x61,0x0,0x75,0x0,0x73,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // previous.svg
  0x0,0xc,
  0x8,0x37,0xc0,0xc7,
  0x0,0x70,
  0x0,0x72,0x0,0x65,0x0,0x76,0x0,0x69,0x0,0x6f,0x0,0x75,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume.svg
  0x0,0xa,
  0xc,0x3b,0xf6,0xa7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // next.svg
  0x0,0x8,
  0xc,0xf7,0x54,0x47,
  0x0,0x6e,
  0x0,0x65,0x0,0x78,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // add.svg
  0x0,0x7,
  0x7,0xa7,0x5a,0x7,
  0x0,0x61,
  0x0,0x64,0x0,0x64,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // app_icon.svg
  0x0,0xc,
  0x7,0x6f,0x91,0x27,
  0x0,0x61,
  0x0,0x70,0x0,0x70,0x0,0x5f,0x0,0x69,0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume_mute.svg
  0x0,0xf,
  0xb,0x22,0xfb,0xc7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x5f,0x0,0x6d,0x0,0x75,0x0,0x74,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // remove.svg
  0x0,0xa,
  0x6,0xcb,0x42,0x47,
  0x0,0x72,
  0x0,0x65,0x0,0x6d,0x0,0x6f,0x0,0x76,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // stop.svg
  0x0,0x8,
  0xb,0x63,0x55,0x87,
  0x0,0x73,
  0x0,0x74,0x0,0x6f,0x0,0x70,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0xa,0x0,0x0,0x0,0x4,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles
  0x0,0x0,0x0,0x10,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles/dark_theme.qss
  0x0,0x0,0x0,0x22,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xd9,0x96,0xa4,0x99,
  // :/icons/play.svg
  0x0,0x0,0x0,0x44,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x4,0xc2,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x6a,0xb1,
  // :/icons/remove.svg
  0x0,0x0,0x1,0x16,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xd,0xb4,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x1a,0x8,
  // :/icons/app_icon.svg
  0x0,0x0,0x0,0xd4,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xb,0x95,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x2f,0xeb,
  // :/icons/add.svg
  0x0,0x0,0x0,0xc0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xa,0xab,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x5,0xd4,
  // :/icons/previous.svg
  0x0,0x0,0x0,0x72,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x6,0xc7,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xbb,0x98,
  // :/icons/volume_mute.svg
  0x0,0x0,0x0,0xf2,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xc,0x8d,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xf2,0x4e,
  // :/icons/stop.svg
  0x0,0x0,0x1,0x30,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xe,0x96,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x93,0x2b,
  // :/icons/volume.svg
  0x0,0x0,0x0,0x90,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x7,0xc5,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xdb,0xe5,
  // :/icons/pause.svg
  0x0,0x0,0x0,0x5a,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x5,0xbe,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x7f,0x6f,
  // :/icons/next.svg
  0x0,0x0,0x0,0xaa,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x9,0xad,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xa7,0x9c,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
