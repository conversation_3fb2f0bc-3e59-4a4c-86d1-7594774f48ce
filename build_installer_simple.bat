@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Audio Player Installer Builder (Simple)
echo ========================================
echo.

REM Set paths (modify these if your installations are different)
set "QT_DIR=C:\Qt\6.9.1\mingw_64"
set "MINGW_DIR=C:\Qt\Tools\mingw1310_64"
set "NSIS_DIR=C:\Program Files (x86)\NSIS"

REM Add Qt and MinGW to PATH
set "PATH=%PATH%;%QT_DIR%\bin;%MINGW_DIR%\bin"

echo [1/5] Checking prerequisites...

REM Check if NSIS is installed
if not exist "%NSIS_DIR%\makensis.exe" (
    echo ERROR: NSIS not found
    echo Expected location: "%NSIS_DIR%\makensis.exe"
    echo.
    echo Please install NSIS from: https://nsis.sourceforge.io/
    echo Or check if NSIS is installed in a different location
    pause
    exit /b 1
)

REM Check if Qt is installed
if not exist "%QT_DIR%\bin\qmake.exe" (
    echo ERROR: Qt not found
    echo Expected location: "%QT_DIR%\bin\qmake.exe"
    echo.
    echo Please check your Qt installation path
    pause
    exit /b 1
)

echo Prerequisites check passed!
echo.

echo [2/5] Cleaning previous build...
if exist build rmdir /s /q build 2>nul
if exist debug rmdir /s /q debug 2>nul
if exist release rmdir /s /q release 2>nul
if exist Makefile del /q Makefile* 2>nul
if exist AudioPlayer_Setup.exe del /q AudioPlayer_Setup.exe 2>nul

echo [3/5] Building application...
echo Running qmake...
qmake AudioPlayer.pro
if !errorlevel! neq 0 (
    echo ERROR: qmake failed
    pause
    exit /b 1
)

echo Running make...
mingw32-make release
if !errorlevel! neq 0 (
    echo ERROR: make failed
    pause
    exit /b 1
)

echo [4/5] Checking build result...
if not exist "build\release\AudioPlayer.exe" (
    echo ERROR: AudioPlayer.exe not found in build\release\
    echo Build may have failed
    pause
    exit /b 1
)

echo Application built successfully!

echo [5/5] Building installer...
echo Running NSIS makensis...
"%NSIS_DIR%\makensis.exe" installer.nsi
if !errorlevel! neq 0 (
    echo ERROR: NSIS installer build failed
    echo.
    echo Possible issues:
    echo - Check that all DLL paths in installer.nsi are correct
    echo - Ensure all required Qt files exist
    echo - Check NSIS script syntax
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS! Installer built successfully!
echo ========================================
echo.

if exist AudioPlayer_Setup.exe (
    echo Installer file: AudioPlayer_Setup.exe
    for %%A in (AudioPlayer_Setup.exe) do echo Size: %%~zA bytes
    echo.
    echo The installer is ready for distribution!
) else (
    echo WARNING: AudioPlayer_Setup.exe not found
    echo The installer build may have failed
)

echo.
pause
