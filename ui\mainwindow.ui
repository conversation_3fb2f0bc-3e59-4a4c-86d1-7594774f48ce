<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Audio Player</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../resources/resources.qrc">
    <normaloff>:/icons/app_icon.svg</normaloff>:/icons/app_icon.svg</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_main">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QSplitter" name="splitter">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QWidget" name="playlistWidget" native="true">
       <property name="minimumSize">
        <size>
         <width>300</width>
         <height>0</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_playlist">
        <property name="spacing">
         <number>5</number>
        </property>
        <property name="leftMargin">
         <number>10</number>
        </property>
        <property name="topMargin">
         <number>10</number>
        </property>
        <property name="rightMargin">
         <number>5</number>
        </property>
        <property name="bottomMargin">
         <number>10</number>
        </property>
        <item>
         <widget class="QLabel" name="playlistLabel">
          <property name="text">
           <string>Playlist</string>
          </property>
          <property name="styleSheet">
           <string>font-weight: bold; font-size: 14px;</string>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="playlistButtonsLayout">
          <item>
           <widget class="QPushButton" name="addFilesButton">
            <property name="text">
             <string>Add Files</string>
            </property>
            <property name="icon">
             <iconset resource="../resources/resources.qrc">
              <normaloff>:/icons/add.svg</normaloff>:/icons/add.svg</iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="removeFileButton">
            <property name="text">
             <string>Remove</string>
            </property>
            <property name="icon">
             <iconset resource="../resources/resources.qrc">
              <normaloff>:/icons/remove.svg</normaloff>:/icons/remove.svg</iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QListView" name="playlistView">
          <property name="dragDropMode">
           <enum>QAbstractItemView::InternalMove</enum>
          </property>
          <property name="alternatingRowColors">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="playerWidget" native="true">
       <layout class="QVBoxLayout" name="verticalLayout_player">
        <property name="spacing">
         <number>10</number>
        </property>
        <property name="leftMargin">
         <number>5</number>
        </property>
        <property name="topMargin">
         <number>10</number>
        </property>
        <property name="rightMargin">
         <number>10</number>
        </property>
        <property name="bottomMargin">
         <number>10</number>
        </property>
        <item>
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QWidget" name="spotifyWidget" native="true">
          <layout class="QVBoxLayout" name="verticalLayout_spotify">
           <property name="spacing">
            <number>10</number>
           </property>
           <property name="leftMargin">
            <number>20</number>
           </property>
           <property name="topMargin">
            <number>10</number>
           </property>
           <property name="rightMargin">
            <number>20</number>
           </property>
           <property name="bottomMargin">
            <number>10</number>
           </property>
           <item>
            <layout class="QHBoxLayout" name="spotifyControlsLayout">
             <item>
              <widget class="QPushButton" name="spotifyLoginButton">
               <property name="text">
                <string>Sign in to Spotify</string>
               </property>
               <property name="icon">
                <iconset resource="../resources/resources.qrc">
                 <normaloff>:/icons/spotify_logo.svg</normaloff>:/icons/spotify_logo.svg</iconset>
               </property>
               <property name="iconSize">
                <size>
                 <width>20</width>
                 <height>20</height>
                </size>
               </property>
               <property name="styleSheet">
                <string>QPushButton { background-color: #1DB954; color: white; font-weight: bold; }</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="spotifyControlsSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="searchLayout">
             <item>
              <spacer name="searchLeftSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLineEdit" name="spotifySearchEdit">
               <property name="minimumSize">
                <size>
                 <width>300</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>500</width>
                 <height>35</height>
                </size>
               </property>
               <property name="placeholderText">
                <string>Search for songs, artists, or albums on Spotify...</string>
               </property>
               <property name="styleSheet">
                <string>QLineEdit {
    border: 2px solid #555555;
    border-radius: 18px;
    padding: 8px 15px;
    font-size: 14px;
    background-color: #404040;
    color: #ffffff;
}
QLineEdit:focus {
    border-color: #1DB954;
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="spotifySearchButton">
               <property name="minimumSize">
                <size>
                 <width>35</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>35</width>
                 <height>35</height>
                </size>
               </property>
               <property name="text">
                <string></string>
               </property>
               <property name="icon">
                <iconset resource="../resources/resources.qrc">
                 <normaloff>:/icons/search.svg</normaloff>:/icons/search.svg</iconset>
               </property>
               <property name="iconSize">
                <size>
                 <width>20</width>
                 <height>20</height>
                </size>
               </property>
               <property name="styleSheet">
                <string>QPushButton {
    border: 2px solid #555555;
    border-radius: 17px;
    background-color: #404040;
}
QPushButton:hover {
    background-color: #1DB954;
    border-color: #1DB954;
}</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="searchRightSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QListWidget" name="spotifySearchResults">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>150</height>
              </size>
             </property>
             <property name="visible">
              <bool>false</bool>
             </property>
             <property name="styleSheet">
              <string>QListWidget {
    background-color: #353535;
    border: 1px solid #555555;
    border-radius: 6px;
    alternate-background-color: #404040;
}
QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #444444;
}
QListWidget::item:hover {
    background-color: #1DB954;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="trackInfoWidget" native="true">
          <layout class="QVBoxLayout" name="verticalLayout_trackInfo">
           <property name="spacing">
            <number>10</number>
           </property>
           <item alignment="Qt::AlignHCenter">
            <widget class="QLabel" name="albumArtLabel">
             <property name="minimumSize">
              <size>
               <width>150</width>
               <height>150</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>150</width>
               <height>150</height>
              </size>
             </property>
             <property name="text">
              <string></string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string>border: 2px solid #555555; border-radius: 8px; background-color: #404040;</string>
             </property>
             <property name="scaledContents">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="trackTitleLabel">
             <property name="text">
              <string>No track selected</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string>font-size: 18px; font-weight: bold;</string>
             </property>
             <property name="wordWrap">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="trackArtistLabel">
             <property name="text">
              <string></string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string>font-size: 14px; color: #cccccc;</string>
             </property>
             <property name="wordWrap">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="trackAlbumLabel">
             <property name="text">
              <string></string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string>font-size: 12px; color: #999999; font-style: italic;</string>
             </property>
             <property name="wordWrap">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="progressWidget" native="true">
          <layout class="QHBoxLayout" name="horizontalLayout_progress">
           <property name="spacing">
            <number>10</number>
           </property>
           <item>
            <widget class="QLabel" name="currentTimeLabel">
             <property name="text">
              <string>00:00</string>
             </property>
             <property name="minimumSize">
              <size>
               <width>40</width>
               <height>0</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSlider" name="progressSlider">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="totalTimeLabel">
             <property name="text">
              <string>00:00</string>
             </property>
             <property name="minimumSize">
              <size>
               <width>40</width>
               <height>0</height>
              </size>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="controlsWidget" native="true">
          <layout class="QHBoxLayout" name="horizontalLayout_controls">
           <property name="spacing">
            <number>10</number>
           </property>
           <item>
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="previousButton">
             <property name="minimumSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string></string>
             </property>
             <property name="icon">
              <iconset resource="../resources/resources.qrc">
               <normaloff>:/icons/previous.svg</normaloff>:/icons/previous.svg</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>32</width>
               <height>32</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="playPauseButton">
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>60</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>60</width>
               <height>60</height>
              </size>
             </property>
             <property name="text">
              <string></string>
             </property>
             <property name="icon">
              <iconset resource="../resources/resources.qrc">
               <normaloff>:/icons/play.svg</normaloff>:/icons/play.svg</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>40</width>
               <height>40</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="stopButton">
             <property name="minimumSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string></string>
             </property>
             <property name="icon">
              <iconset resource="../resources/resources.qrc">
               <normaloff>:/icons/stop.svg</normaloff>:/icons/stop.svg</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>32</width>
               <height>32</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="nextButton">
             <property name="minimumSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="text">
              <string></string>
             </property>
             <property name="icon">
              <iconset resource="../resources/resources.qrc">
               <normaloff>:/icons/next.svg</normaloff>:/icons/next.svg</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>32</width>
               <height>32</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="volumeWidget" native="true">
          <layout class="QHBoxLayout" name="horizontalLayout_volume">
           <property name="spacing">
            <number>10</number>
           </property>
           <item>
            <spacer name="horizontalSpacer_4">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="muteButton">
             <property name="minimumSize">
              <size>
               <width>30</width>
               <height>30</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>30</width>
               <height>30</height>
              </size>
             </property>
             <property name="text">
              <string></string>
             </property>
             <property name="icon">
              <iconset resource="../resources/resources.qrc">
               <normaloff>:/icons/volume.svg</normaloff>:/icons/volume.svg</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>20</width>
               <height>20</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSlider" name="volumeSlider">
             <property name="maximum">
              <number>100</number>
             </property>
             <property name="value">
              <number>70</number>
             </property>
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_5">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_2">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1000</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <addaction name="actionOpen_Files"/>
    <addaction name="separator"/>
    <addaction name="actionNew_Playlist"/>
    <addaction name="actionOpen_Playlist"/>
    <addaction name="actionSave_Playlist"/>
    <addaction name="actionSave_Playlist_As"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuPlayback">
    <property name="title">
     <string>Playback</string>
    </property>
    <addaction name="actionPlay_Pause"/>
    <addaction name="actionStop"/>
    <addaction name="actionNext"/>
    <addaction name="actionPrevious"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>Help</string>
    </property>
    <addaction name="actionAbout"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuPlayback"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionOpen_Files">
   <property name="text">
    <string>Open Files...</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+O</string>
   </property>
  </action>
  <action name="actionExit">
   <property name="text">
    <string>Exit</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Q</string>
   </property>
  </action>
  <action name="actionPlay_Pause">
   <property name="text">
    <string>Play/Pause</string>
   </property>
   <property name="shortcut">
    <string>Space</string>
   </property>
  </action>
  <action name="actionStop">
   <property name="text">
    <string>Stop</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+.</string>
   </property>
  </action>
  <action name="actionNext">
   <property name="text">
    <string>Next</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Right</string>
   </property>
  </action>
  <action name="actionPrevious">
   <property name="text">
    <string>Previous</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Left</string>
   </property>
  </action>
  <action name="actionNew_Playlist">
   <property name="text">
    <string>New Playlist</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+N</string>
   </property>
  </action>
  <action name="actionOpen_Playlist">
   <property name="text">
    <string>Open Playlist...</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Shift+O</string>
   </property>
  </action>
  <action name="actionSave_Playlist">
   <property name="text">
    <string>Save Playlist</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
  </action>
  <action name="actionSave_Playlist_As">
   <property name="text">
    <string>Save Playlist As...</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Shift+S</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>About</string>
   </property>
  </action>
 </widget>
 <resources>
  <include location="../resources/resources.qrc"/>
 </resources>
 <connections/>
</ui>
