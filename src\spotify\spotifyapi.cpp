#include "spotify/spotifyapi.h"
#include "spotify/spotifyauth.h"
#include "spotify/spotifytrack.h"
#include <QJsonDocument>
#include <QUrlQuery>
#include <QTimer>
#include <QJsonArray>
#include <QDebug>

const QString SpotifyAPI::SPOTIFY_API_BASE_URL = "https://api.spotify.com/v1";

SpotifyAPI::SpotifyAPI(SpotifyAuth *auth, QObject *parent)
    : QObject(parent)
    , m_auth(auth)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_rateLimitDelay(100) // 100ms default delay between requests
{
}

void SpotifyAPI::searchTracks(const QString &query, int limit, int offset)
{
    QUrl url(SPOTIFY_API_BASE_URL + "/search");
    QUrlQuery urlQuery;
    urlQuery.addQueryItem("q", query);
    urlQuery.addQueryItem("type", "track");
    urlQuery.addQueryItem("limit", QString::number(limit));
    urlQuery.addQueryItem("offset", QString::number(offset));
    url.setQuery(urlQuery);

    QJsonObject metadata;
    metadata["query"] = query;
    metadata["limit"] = limit;
    metadata["offset"] = offset;

    makeRequest(SearchTracks, url, metadata);
}

void SpotifyAPI::searchArtists(const QString &query, int limit, int offset)
{
    QUrl url(SPOTIFY_API_BASE_URL + "/search");
    QUrlQuery urlQuery;
    urlQuery.addQueryItem("q", query);
    urlQuery.addQueryItem("type", "artist");
    urlQuery.addQueryItem("limit", QString::number(limit));
    urlQuery.addQueryItem("offset", QString::number(offset));
    url.setQuery(urlQuery);

    QJsonObject metadata;
    metadata["query"] = query;
    metadata["limit"] = limit;
    metadata["offset"] = offset;

    makeRequest(SearchArtists, url, metadata);
}

void SpotifyAPI::searchAlbums(const QString &query, int limit, int offset)
{
    QUrl url(SPOTIFY_API_BASE_URL + "/search");
    QUrlQuery urlQuery;
    urlQuery.addQueryItem("q", query);
    urlQuery.addQueryItem("type", "album");
    urlQuery.addQueryItem("limit", QString::number(limit));
    urlQuery.addQueryItem("offset", QString::number(offset));
    url.setQuery(urlQuery);

    QJsonObject metadata;
    metadata["query"] = query;
    metadata["limit"] = limit;
    metadata["offset"] = offset;

    makeRequest(SearchAlbums, url, metadata);
}

void SpotifyAPI::searchPlaylists(const QString &query, int limit, int offset)
{
    QUrl url(SPOTIFY_API_BASE_URL + "/search");
    QUrlQuery urlQuery;
    urlQuery.addQueryItem("q", query);
    urlQuery.addQueryItem("type", "playlist");
    urlQuery.addQueryItem("limit", QString::number(limit));
    urlQuery.addQueryItem("offset", QString::number(offset));
    url.setQuery(urlQuery);

    QJsonObject metadata;
    metadata["query"] = query;
    metadata["limit"] = limit;
    metadata["offset"] = offset;

    makeRequest(SearchPlaylists, url, metadata);
}

void SpotifyAPI::searchAll(const QString &query, int limit, int offset)
{
    QUrl url(SPOTIFY_API_BASE_URL + "/search");
    QUrlQuery urlQuery;
    urlQuery.addQueryItem("q", query);
    urlQuery.addQueryItem("type", "track,artist,album,playlist");
    urlQuery.addQueryItem("limit", QString::number(limit));
    urlQuery.addQueryItem("offset", QString::number(offset));
    url.setQuery(urlQuery);

    QJsonObject metadata;
    metadata["query"] = query;
    metadata["limit"] = limit;
    metadata["offset"] = offset;

    makeRequest(SearchAll, url, metadata);
}

void SpotifyAPI::getTrack(const QString &trackId)
{
    QUrl url(SPOTIFY_API_BASE_URL + "/tracks/" + trackId);
    makeRequest(GetTrack, url);
}

void SpotifyAPI::getTracks(const QStringList &trackIds)
{
    QUrl url(SPOTIFY_API_BASE_URL + "/tracks");
    QUrlQuery urlQuery;
    urlQuery.addQueryItem("ids", trackIds.join(","));
    url.setQuery(urlQuery);

    makeRequest(GetTracks, url);
}

void SpotifyAPI::getUserSavedTracks(int limit, int offset)
{
    QUrl url(SPOTIFY_API_BASE_URL + "/me/tracks");
    QUrlQuery urlQuery;
    urlQuery.addQueryItem("limit", QString::number(limit));
    urlQuery.addQueryItem("offset", QString::number(offset));
    url.setQuery(urlQuery);

    QJsonObject metadata;
    metadata["limit"] = limit;
    metadata["offset"] = offset;

    makeRequest(GetUserSavedTracks, url, metadata);
}

void SpotifyAPI::getCurrentUser()
{
    QUrl url(SPOTIFY_API_BASE_URL + "/me");
    makeRequest(GetCurrentUser, url);
}

bool SpotifyAPI::isAuthenticated() const
{
    return m_auth && m_auth->isAuthenticated();
}

void SpotifyAPI::setRateLimitDelay(int milliseconds)
{
    m_rateLimitDelay = milliseconds;
}

QNetworkRequest SpotifyAPI::createAuthenticatedRequest(const QUrl &url)
{
    QNetworkRequest request(url);
    
    if (m_auth && m_auth->isAuthenticated()) {
        QString authHeader = "Bearer " + m_auth->accessToken();
        request.setRawHeader("Authorization", authHeader.toUtf8());
    }
    
    request.setRawHeader("Content-Type", "application/json");
    return request;
}

void SpotifyAPI::makeRequest(RequestType type, const QUrl &url, const QJsonObject &metadata)
{
    if (!isAuthenticated()) {
        emit apiError(url.toString(), 401, "Not authenticated");
        return;
    }

    // For demo purposes, simulate API responses
    if (type == SearchTracks) {
        QTimer::singleShot(500, [this, metadata]() {
            simulateSearchResponse(metadata);
        });
        return;
    }

    // For real implementation, uncomment below:
    /*
    QNetworkRequest request = createAuthenticatedRequest(url);
    QNetworkReply *reply = m_networkManager->get(request);

    PendingRequest pendingRequest;
    pendingRequest.type = type;
    pendingRequest.reply = reply;
    pendingRequest.metadata = metadata;

    m_pendingRequests[reply] = pendingRequest;

    connect(reply, &QNetworkReply::finished, this, &SpotifyAPI::onReplyFinished);
    */
}

void SpotifyAPI::onReplyFinished()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply || !m_pendingRequests.contains(reply)) {
        return;
    }

    PendingRequest request = m_pendingRequests.take(reply);
    
    QByteArray data = reply->readAll();
    int statusCode = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
    
    if (reply->error() != QNetworkReply::NoError) {
        emit networkError(reply->url().toString(), reply->errorString());
    } else {
        handleResponse(request, data, statusCode);
    }
    
    reply->deleteLater();
}

void SpotifyAPI::handleResponse(const PendingRequest &request, const QByteArray &data, int statusCode)
{
    if (statusCode != 200) {
        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject errorObj = doc.object();
        QString errorMessage = errorObj["error"].toObject()["message"].toString();
        emit apiError(request.reply->url().toString(), statusCode, errorMessage);
        return;
    }

    QJsonDocument doc = QJsonDocument::fromJson(data);
    QJsonObject response = doc.object();

    switch (request.type) {
    case SearchTracks:
        handleSearchTracksResponse(response, request.metadata);
        break;
    case SearchArtists:
        handleSearchArtistsResponse(response, request.metadata);
        break;
    case SearchAlbums:
        handleSearchAlbumsResponse(response, request.metadata);
        break;
    case SearchPlaylists:
        handleSearchPlaylistsResponse(response, request.metadata);
        break;
    case SearchAll:
        handleSearchAllResponse(response);
        break;
    case GetTrack:
        handleTrackResponse(response);
        break;
    case GetTracks:
        handleTracksResponse(response);
        break;
    case GetUserSavedTracks:
        handleUserSavedTracksResponse(response, request.metadata);
        break;
    case GetCurrentUser:
        handleCurrentUserResponse(response);
        break;
    default:
        qDebug() << "Unhandled request type:" << request.type;
        break;
    }
}

void SpotifyAPI::handleSearchTracksResponse(const QJsonObject &response, const QJsonObject &metadata)
{
    QJsonObject tracks = response["tracks"].toObject();
    QJsonArray items = tracks["items"].toArray();
    int total = tracks["total"].toInt();
    int offset = metadata["offset"].toInt();

    QList<SpotifyTrack*> trackList = parseTracksFromTracks(items);
    emit searchTracksFinished(trackList, total, offset);
}

void SpotifyAPI::handleSearchArtistsResponse(const QJsonObject &response, const QJsonObject &metadata)
{
    QJsonObject artists = response["artists"].toObject();
    QJsonArray items = artists["items"].toArray();
    int total = artists["total"].toInt();
    int offset = metadata["offset"].toInt();

    emit searchArtistsFinished(items, total, offset);
}

void SpotifyAPI::handleSearchAlbumsResponse(const QJsonObject &response, const QJsonObject &metadata)
{
    QJsonObject albums = response["albums"].toObject();
    QJsonArray items = albums["items"].toArray();
    int total = albums["total"].toInt();
    int offset = metadata["offset"].toInt();

    emit searchAlbumsFinished(items, total, offset);
}

void SpotifyAPI::handleSearchPlaylistsResponse(const QJsonObject &response, const QJsonObject &metadata)
{
    QJsonObject playlists = response["playlists"].toObject();
    QJsonArray items = playlists["items"].toArray();
    int total = playlists["total"].toInt();
    int offset = metadata["offset"].toInt();

    emit searchPlaylistsFinished(items, total, offset);
}

void SpotifyAPI::handleSearchAllResponse(const QJsonObject &response)
{
    emit searchAllFinished(response);
}

void SpotifyAPI::handleTrackResponse(const QJsonObject &response)
{
    SpotifyTrack *track = new SpotifyTrack(response, this);
    emit trackReceived(track);
}

void SpotifyAPI::handleTracksResponse(const QJsonObject &response)
{
    QJsonArray tracks = response["tracks"].toArray();
    QList<SpotifyTrack*> trackList = parseTracksFromTracks(tracks);
    emit tracksReceived(trackList);
}

void SpotifyAPI::handleUserSavedTracksResponse(const QJsonObject &response, const QJsonObject &metadata)
{
    QJsonArray items = response["items"].toArray();
    int total = response["total"].toInt();
    int offset = metadata["offset"].toInt();

    QList<SpotifyTrack*> trackList = parseTracksFromItems(items);
    emit userSavedTracksReceived(trackList, total, offset);
}

void SpotifyAPI::handleCurrentUserResponse(const QJsonObject &response)
{
    emit currentUserReceived(response);
}

QList<SpotifyTrack*> SpotifyAPI::parseTracksFromItems(const QJsonArray &items)
{
    QList<SpotifyTrack*> tracks;
    for (const QJsonValue &value : items) {
        QJsonObject item = value.toObject();
        QJsonObject track = item["track"].toObject();
        tracks.append(new SpotifyTrack(track, this));
    }
    return tracks;
}

QList<SpotifyTrack*> SpotifyAPI::parseTracksFromTracks(const QJsonArray &tracks)
{
    QList<SpotifyTrack*> trackList;
    for (const QJsonValue &value : tracks) {
        QJsonObject track = value.toObject();
        trackList.append(new SpotifyTrack(track, this));
    }
    return trackList;
}

void SpotifyAPI::simulateSearchResponse(const QJsonObject &metadata)
{
    QString query = metadata["query"].toString();

    // Create demo tracks
    QList<SpotifyTrack*> tracks;

    QStringList demoArtists = {"The Beatles", "Queen", "Led Zeppelin", "Pink Floyd", "The Rolling Stones"};
    QStringList demoAlbums = {"Greatest Hits", "Live Album", "Studio Collection", "Best Of", "Anthology"};

    for (int i = 0; i < 5; ++i) {
        // Create demo track JSON
        QJsonObject trackJson;
        trackJson["id"] = QString("demo_track_%1").arg(i);
        trackJson["name"] = QString("%1 Song %2").arg(query).arg(i + 1);
        trackJson["uri"] = QString("spotify:track:demo_%1").arg(i);
        trackJson["duration_ms"] = 180000 + (i * 30000); // 3-4.5 minutes
        trackJson["popularity"] = 70 + (i * 5);
        trackJson["explicit"] = false;
        // Use a real audio file for demo (this is a public domain audio file)
        trackJson["preview_url"] = "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav";

        // Demo artists
        QJsonArray artistsArray;
        QJsonObject artist;
        artist["id"] = QString("demo_artist_%1").arg(i);
        artist["name"] = demoArtists[i % demoArtists.size()];
        artistsArray.append(artist);
        trackJson["artists"] = artistsArray;

        // Demo album
        QJsonObject album;
        album["id"] = QString("demo_album_%1").arg(i);
        album["name"] = demoAlbums[i % demoAlbums.size()];
        album["release_date"] = QString("202%1-01-01").arg(i);

        // Demo album images
        QJsonArray imagesArray;
        QJsonObject image;
        image["url"] = "https://via.placeholder.com/300x300/1DB954/FFFFFF?text=Demo+Album";
        image["height"] = 300;
        image["width"] = 300;
        imagesArray.append(image);
        album["images"] = imagesArray;

        trackJson["album"] = album;

        // Create SpotifyTrack object
        SpotifyTrack *track = new SpotifyTrack(trackJson, this);
        tracks.append(track);
    }

    // Emit the results
    emit searchTracksFinished(tracks, tracks.size(), 0);
}
