#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QApplication>
#include <QFileDialog>
#include <QMessageBox>
#include <QSettings>
#include <QStandardPaths>
#include <QDir>
#include <QMimeData>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QUrl>
#include <QDebug>
#include <algorithm>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_audioPlayer(new AudioPlayer(this))
    , m_playlist(new Playlist(this))
    , m_playlistModel(new PlaylistModel(m_playlist, this))
    , m_positionTimer(new QTimer(this))
    , m_spotifyAuth(new SpotifyAuth(this))
    , m_spotifyAPI(new SpotifyAPI(m_spotifyAuth, this))
    , m_playlistModified(false)
{
    ui->setupUi(this);
    
    // Set up playlist model
    ui->playlistView->setModel(m_playlistModel);
    
    // Enable drag and drop
    setAcceptDrops(true);
    ui->playlistView->setDragDropMode(QAbstractItemView::InternalMove);
    
    // Setup connections
    setupConnections();
    
    // Initialize UI state
    updatePlayPauseButton();
    updateTrackInfo();
    
    // Set initial volume
    ui->volumeSlider->setValue(70);
    m_audioPlayer->setVolume(0.7f);
    
    // Start position timer
    m_positionTimer->setInterval(100); // Update every 100ms
    m_positionTimer->start();

    // Configure Spotify
    setupSpotify();

    // Load settings
    loadSettings();
}

MainWindow::~MainWindow()
{
    saveSettings();
    delete ui;
}

void MainWindow::setupConnections()
{
    // Audio player connections
    connect(m_audioPlayer, &AudioPlayer::playbackStateChanged,
            this, &MainWindow::onPlaybackStateChanged);
    connect(m_audioPlayer, &AudioPlayer::positionChanged,
            this, &MainWindow::onPositionChanged);
    connect(m_audioPlayer, &AudioPlayer::durationChanged,
            this, &MainWindow::onDurationChanged);
    connect(m_audioPlayer, &AudioPlayer::volumeChanged,
            this, &MainWindow::onVolumeChanged);
    connect(m_audioPlayer, &AudioPlayer::mutedChanged,
            this, &MainWindow::onMutedChanged);
    connect(m_audioPlayer, &AudioPlayer::errorOccurred,
            this, &MainWindow::onErrorOccurred);
    
    // Control connections
    connect(ui->playPauseButton, &QPushButton::clicked,
            this, &MainWindow::playPause);
    connect(ui->stopButton, &QPushButton::clicked,
            this, &MainWindow::stop);
    connect(ui->nextButton, &QPushButton::clicked,
            this, &MainWindow::next);
    connect(ui->previousButton, &QPushButton::clicked,
            this, &MainWindow::previous);
    connect(ui->progressSlider, &QSlider::sliderPressed, [this]() {
        m_positionTimer->stop();
    });
    connect(ui->progressSlider, &QSlider::sliderReleased, [this]() {
        setPosition(ui->progressSlider->value());
        m_positionTimer->start();
    });
    connect(ui->volumeSlider, &QSlider::valueChanged,
            this, &MainWindow::setVolume);
    connect(ui->muteButton, &QPushButton::clicked,
            this, &MainWindow::toggleMute);
    
    // Playlist connections
    connect(ui->addFilesButton, &QPushButton::clicked,
            this, &MainWindow::addFiles);
    connect(ui->removeFileButton, &QPushButton::clicked,
            this, &MainWindow::removeSelectedItems);
    connect(ui->playlistView, &QListView::doubleClicked,
            this, &MainWindow::onPlaylistItemDoubleClicked);
    connect(m_playlist, &Playlist::currentIndexChanged,
            this, &MainWindow::onCurrentPlaylistItemChanged);
    
    // Menu actions
    connect(ui->actionOpen_Files, &QAction::triggered,
            this, &MainWindow::openFiles);
    connect(ui->actionNew_Playlist, &QAction::triggered,
            this, &MainWindow::newPlaylist);
    connect(ui->actionOpen_Playlist, &QAction::triggered,
            this, &MainWindow::openPlaylist);
    connect(ui->actionSave_Playlist, &QAction::triggered,
            this, &MainWindow::savePlaylist);
    connect(ui->actionSave_Playlist_As, &QAction::triggered,
            this, &MainWindow::savePlaylistAs);
    connect(ui->actionExit, &QAction::triggered,
            this, &QWidget::close);
    connect(ui->actionPlay_Pause, &QAction::triggered,
            this, &MainWindow::playPause);
    connect(ui->actionStop, &QAction::triggered,
            this, &MainWindow::stop);
    connect(ui->actionNext, &QAction::triggered,
            this, &MainWindow::next);
    connect(ui->actionPrevious, &QAction::triggered,
            this, &MainWindow::previous);
    connect(ui->actionAbout, &QAction::triggered,
            this, &MainWindow::about);
    
    // Timer connection
    connect(m_positionTimer, &QTimer::timeout,
            this, &MainWindow::updatePosition);

    // Spotify UI connections
    connect(ui->spotifyLoginButton, &QPushButton::clicked,
            this, &MainWindow::onSpotifyLoginClicked);
    connect(ui->spotifySearchButton, &QPushButton::clicked,
            this, &MainWindow::onSpotifySearchClicked);
    connect(ui->spotifySearchEdit, &QLineEdit::textChanged,
            this, &MainWindow::onSpotifySearchTextChanged);
    connect(ui->spotifySearchEdit, &QLineEdit::returnPressed,
            this, &MainWindow::onSpotifySearchClicked);
    connect(ui->spotifySearchResults, &QListWidget::itemClicked,
            this, &MainWindow::onSpotifySearchResultClicked);

    // Spotify authentication connections
    connect(m_spotifyAuth, &SpotifyAuth::authenticationSucceeded,
            this, &MainWindow::onSpotifyAuthenticationSucceeded);
    connect(m_spotifyAuth, &SpotifyAuth::authenticationFailed,
            this, &MainWindow::onSpotifyAuthenticationFailed);
    connect(m_spotifyAuth, &SpotifyAuth::loggedOut,
            this, &MainWindow::onSpotifyLoggedOut);

    // Spotify API connections
    connect(m_spotifyAPI, &SpotifyAPI::searchTracksFinished,
            this, &MainWindow::onSpotifySearchTracksFinished);
    connect(m_spotifyAPI, &SpotifyAPI::apiError,
            this, &MainWindow::onSpotifyApiError);
    connect(m_spotifyAPI, &SpotifyAPI::networkError,
            this, &MainWindow::onSpotifyNetworkError);
}

void MainWindow::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
    case Qt::Key_Space:
        playPause();
        event->accept();
        break;
    case Qt::Key_Right:
        if (event->modifiers() & Qt::ControlModifier) {
            next();
            event->accept();
        }
        break;
    case Qt::Key_Left:
        if (event->modifiers() & Qt::ControlModifier) {
            previous();
            event->accept();
        }
        break;
    case Qt::Key_Period:
        if (event->modifiers() & Qt::ControlModifier) {
            stop();
            event->accept();
        }
        break;
    default:
        QMainWindow::keyPressEvent(event);
    }
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    if (confirmPlaylistDiscard()) {
        saveSettings();
        event->accept();
    } else {
        event->ignore();
    }
}

void MainWindow::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        // Check if any of the URLs are audio files
        QList<QUrl> urls = event->mimeData()->urls();
        QStringList supportedExtensions = getSupportedAudioFiles();

        for (const QUrl &url : urls) {
            if (url.isLocalFile()) {
                QString fileName = url.toLocalFile();
                for (const QString &ext : supportedExtensions) {
                    QString pattern = ext.mid(1); // Remove the '*' from pattern
                    if (fileName.endsWith(pattern, Qt::CaseInsensitive)) {
                        event->acceptProposedAction();
                        return;
                    }
                }
            }
        }
    }
    event->ignore();
}

void MainWindow::dropEvent(QDropEvent *event)
{
    QList<QUrl> urls = event->mimeData()->urls();
    QList<QUrl> audioUrls;
    QStringList supportedExtensions = getSupportedAudioFiles();

    for (const QUrl &url : urls) {
        if (url.isLocalFile()) {
            QString fileName = url.toLocalFile();
            for (const QString &ext : supportedExtensions) {
                QString pattern = ext.mid(1); // Remove the '*' from pattern
                if (fileName.endsWith(pattern, Qt::CaseInsensitive)) {
                    audioUrls.append(url);
                    break;
                }
            }
        }
    }

    if (!audioUrls.isEmpty()) {
        m_playlist->addItems(audioUrls);
        m_playlistModified = true;
        ui->statusbar->showMessage(QString("Added %1 files via drag and drop").arg(audioUrls.count()), 2000);
        event->acceptProposedAction();
    } else {
        event->ignore();
    }
}

// Audio player slots
void MainWindow::onPlaybackStateChanged(AudioPlayer::PlaybackState state)
{
    Q_UNUSED(state)
    updatePlayPauseButton();
    updateTrackInfo();
}

void MainWindow::onPositionChanged(qint64 position)
{
    if (!ui->progressSlider->isSliderDown()) {
        ui->progressSlider->setValue(static_cast<int>(position));
    }
    updateTimeLabels(position, m_audioPlayer->duration());
}

void MainWindow::onDurationChanged(qint64 duration)
{
    ui->progressSlider->setMaximum(static_cast<int>(duration));
    updateTimeLabels(m_audioPlayer->position(), duration);
}

void MainWindow::onVolumeChanged(float volume)
{
    ui->volumeSlider->setValue(static_cast<int>(volume * 100));
}

void MainWindow::onMutedChanged(bool muted)
{
    if (muted) {
        ui->muteButton->setIcon(QIcon(":/icons/volume_mute.svg"));
    } else {
        ui->muteButton->setIcon(QIcon(":/icons/volume.svg"));
    }
}

void MainWindow::onErrorOccurred(const QString &error)
{
    QMessageBox::warning(this, "Playback Error", error);
    ui->statusbar->showMessage("Error: " + error, 5000);
}

// Control slots
void MainWindow::playPause()
{
    if (m_playlist->isEmpty()) {
        addFiles();
        return;
    }
    
    switch (m_audioPlayer->playbackState()) {
    case AudioPlayer::PlayingState:
        m_audioPlayer->pause();
        break;
    case AudioPlayer::PausedState:
    case AudioPlayer::StoppedState:
        if (m_playlist->currentUrl().isValid()) {
            m_audioPlayer->setSource(m_playlist->currentUrl());
            m_audioPlayer->play();
        }
        break;
    }
}

void MainWindow::stop()
{
    m_audioPlayer->stop();
}

void MainWindow::next()
{
    if (m_playlist->next()) {
        m_audioPlayer->setSource(m_playlist->currentUrl());
        if (m_audioPlayer->playbackState() == AudioPlayer::PlayingState) {
            m_audioPlayer->play();
        }
    }
}

void MainWindow::previous()
{
    if (m_playlist->previous()) {
        m_audioPlayer->setSource(m_playlist->currentUrl());
        if (m_audioPlayer->playbackState() == AudioPlayer::PlayingState) {
            m_audioPlayer->play();
        }
    }
}

void MainWindow::setPosition(int position)
{
    m_audioPlayer->setPosition(position);
}

void MainWindow::setVolume(int volume)
{
    m_audioPlayer->setVolume(volume / 100.0f);
}

void MainWindow::toggleMute()
{
    m_audioPlayer->setMuted(!m_audioPlayer->isMuted());
}

// Playlist slots
void MainWindow::addFiles()
{
    QStringList files = QFileDialog::getOpenFileNames(
        this,
        "Add Audio Files",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation),
        getAudioFileFilters().join(";;")
    );

    if (!files.isEmpty()) {
        QList<QUrl> urls;
        for (const QString &file : files) {
            urls.append(QUrl::fromLocalFile(file));
        }
        m_playlist->addItems(urls);
        m_playlistModified = true;
        ui->statusbar->showMessage(QString("Added %1 files").arg(files.count()), 2000);
    }
}

void MainWindow::removeSelectedItems()
{
    QModelIndexList selected = ui->playlistView->selectionModel()->selectedIndexes();
    if (selected.isEmpty()) {
        return;
    }

    // Sort in descending order to remove from the end first
    std::sort(selected.begin(), selected.end(), [](const QModelIndex &a, const QModelIndex &b) {
        return a.row() > b.row();
    });

    for (const QModelIndex &index : selected) {
        m_playlist->removeItem(index.row());
    }

    m_playlistModified = true;
    ui->statusbar->showMessage("Removed selected items", 2000);
}

void MainWindow::onPlaylistItemDoubleClicked(const QModelIndex &index)
{
    if (index.isValid()) {
        m_playlist->setCurrentIndex(index.row());
        m_audioPlayer->setSource(m_playlist->currentUrl());
        m_audioPlayer->play();
    }
}

void MainWindow::onCurrentPlaylistItemChanged(int index)
{
    Q_UNUSED(index)
    updateTrackInfo();

    // Update selection in the list view
    if (index >= 0) {
        QModelIndex modelIndex = m_playlistModel->index(index, 0);
        ui->playlistView->setCurrentIndex(modelIndex);
    }
}

// Menu actions
void MainWindow::openFiles()
{
    addFiles();
}

void MainWindow::newPlaylist()
{
    if (!confirmPlaylistDiscard()) {
        return;
    }

    m_playlist->clear();
    m_currentPlaylistFile.clear();
    m_playlistModified = false;
    ui->statusbar->showMessage("New playlist created", 2000);
}

void MainWindow::openPlaylist()
{
    if (!confirmPlaylistDiscard()) {
        return;
    }

    QString fileName = QFileDialog::getOpenFileName(
        this,
        "Open Playlist",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation),
        "Playlist Files (*.m3u *.m3u8);;All Files (*)"
    );

    if (!fileName.isEmpty()) {
        if (m_playlist->load(fileName)) {
            m_currentPlaylistFile = fileName;
            m_playlistModified = false;
            ui->statusbar->showMessage("Playlist loaded: " + QFileInfo(fileName).baseName(), 2000);
        } else {
            QMessageBox::warning(this, "Error", "Failed to load playlist file.");
        }
    }
}

void MainWindow::savePlaylist()
{
    if (m_currentPlaylistFile.isEmpty()) {
        savePlaylistAs();
        return;
    }

    if (m_playlist->save(m_currentPlaylistFile)) {
        m_playlistModified = false;
        ui->statusbar->showMessage("Playlist saved", 2000);
    } else {
        QMessageBox::warning(this, "Error", "Failed to save playlist file.");
    }
}

void MainWindow::savePlaylistAs()
{
    QString fileName = QFileDialog::getSaveFileName(
        this,
        "Save Playlist As",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation) + "/playlist.m3u",
        "M3U Playlist (*.m3u);;M3U8 Playlist (*.m3u8);;All Files (*)"
    );

    if (!fileName.isEmpty()) {
        if (m_playlist->save(fileName)) {
            m_currentPlaylistFile = fileName;
            m_playlistModified = false;
            ui->statusbar->showMessage("Playlist saved as: " + QFileInfo(fileName).baseName(), 2000);
        } else {
            QMessageBox::warning(this, "Error", "Failed to save playlist file.");
        }
    }
}

void MainWindow::about()
{
    QMessageBox::about(this, "About Audio Player",
        "<h2>Audio Player 1.0</h2>"
        "<p>A modern Qt-based audio player with playlist support.</p>"
        "<p>Features:</p>"
        "<ul>"
        "<li>Support for MP3, WAV, FLAC, and other audio formats</li>"
        "<li>Playlist management with drag-and-drop reordering</li>"
        "<li>Dark theme interface</li>"
        "<li>Keyboard shortcuts</li>"
        "</ul>"
        "<p>Built with Qt 6 and C++</p>"
    );
}

void MainWindow::updatePosition()
{
    // This is called by the timer to update position when not dragging
    if (!ui->progressSlider->isSliderDown()) {
        qint64 position = m_audioPlayer->position();
        ui->progressSlider->setValue(static_cast<int>(position));
        updateTimeLabels(position, m_audioPlayer->duration());
    }
}

// Helper methods
void MainWindow::updatePlayPauseButton()
{
    switch (m_audioPlayer->playbackState()) {
    case AudioPlayer::PlayingState:
        ui->playPauseButton->setIcon(QIcon(":/icons/pause.svg"));
        ui->actionPlay_Pause->setText("Pause");
        break;
    case AudioPlayer::PausedState:
    case AudioPlayer::StoppedState:
        ui->playPauseButton->setIcon(QIcon(":/icons/play.svg"));
        ui->actionPlay_Pause->setText("Play");
        break;
    }
}

void MainWindow::updateTrackInfo()
{
    PlaylistItem currentItem = m_playlist->currentItem();

    if (currentItem.url.isValid()) {
        ui->trackTitleLabel->setText(currentItem.title);

        // Set artist name
        if (!currentItem.artist.isEmpty()) {
            ui->trackArtistLabel->setText(currentItem.artist);
        } else {
            ui->trackArtistLabel->setText("Unknown Artist");
        }

        // Set album name
        if (!currentItem.album.isEmpty()) {
            ui->trackAlbumLabel->setText(currentItem.album);
        } else {
            ui->trackAlbumLabel->setText("");
        }

        // Update album art
        updateAlbumArt(currentItem);

        QString statusText = QString("Playing: %1").arg(currentItem.displayText());
        if (m_audioPlayer->playbackState() == AudioPlayer::PlayingState) {
            ui->statusbar->showMessage(statusText);
        }
    } else {
        ui->trackTitleLabel->setText("No track selected");
        ui->trackArtistLabel->setText("");
        ui->trackAlbumLabel->setText("");

        // Set default album art
        QPixmap defaultArt(":/icons/default_album_art.svg");
        ui->albumArtLabel->setPixmap(defaultArt.scaled(150, 150, Qt::KeepAspectRatio, Qt::SmoothTransformation));

        ui->statusbar->clearMessage();
    }
}

void MainWindow::updateAlbumArt(const PlaylistItem &item)
{
    QPixmap albumArt;
    bool artLoaded = false;

    // Try to load album art from the specified path
    if (!item.albumArtPath.isEmpty() && QFile::exists(item.albumArtPath)) {
        artLoaded = albumArt.load(item.albumArtPath);
    }

    // If no custom art, try to find album art in the same directory as the audio file
    if (!artLoaded && item.url.isLocalFile()) {
        QFileInfo audioFile(item.url.toLocalFile());
        QDir audioDir = audioFile.dir();

        // Common album art filenames
        QStringList artNames = {"cover.jpg", "cover.png", "album.jpg", "album.png",
                               "folder.jpg", "folder.png", "artwork.jpg", "artwork.png"};

        for (const QString &artName : artNames) {
            QString artPath = audioDir.absoluteFilePath(artName);
            if (QFile::exists(artPath)) {
                artLoaded = albumArt.load(artPath);
                if (artLoaded) break;
            }
        }
    }

    // If still no art found, use default
    if (!artLoaded) {
        albumArt.load(":/icons/default_album_art.svg");
    }

    // Scale and set the album art
    QPixmap scaledArt = albumArt.scaled(150, 150, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    ui->albumArtLabel->setPixmap(scaledArt);
}

void MainWindow::updateTimeLabels(qint64 position, qint64 duration)
{
    ui->currentTimeLabel->setText(formatTime(position));
    ui->totalTimeLabel->setText(formatTime(duration));
}

QString MainWindow::formatTime(qint64 milliseconds) const
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds %= 60;

    return QString("%1:%2").arg(minutes).arg(seconds, 2, 10, QChar('0'));
}

void MainWindow::loadSettings()
{
    QSettings settings;

    // Window geometry
    restoreGeometry(settings.value("geometry").toByteArray());
    restoreState(settings.value("windowState").toByteArray());

    // Volume
    int volume = settings.value("volume", 70).toInt();
    ui->volumeSlider->setValue(volume);
    m_audioPlayer->setVolume(volume / 100.0f);

    // Mute state
    bool muted = settings.value("muted", false).toBool();
    m_audioPlayer->setMuted(muted);

    // Last playlist
    QString lastPlaylist = settings.value("lastPlaylist").toString();
    if (!lastPlaylist.isEmpty() && QFile::exists(lastPlaylist)) {
        m_playlist->load(lastPlaylist);
        m_currentPlaylistFile = lastPlaylist;
        m_playlistModified = false;
    }
}

void MainWindow::saveSettings()
{
    QSettings settings;

    // Window geometry
    settings.setValue("geometry", saveGeometry());
    settings.setValue("windowState", saveState());

    // Volume
    settings.setValue("volume", ui->volumeSlider->value());
    settings.setValue("muted", m_audioPlayer->isMuted());

    // Last playlist
    if (!m_currentPlaylistFile.isEmpty()) {
        settings.setValue("lastPlaylist", m_currentPlaylistFile);
    }
}

bool MainWindow::confirmPlaylistDiscard()
{
    if (!m_playlistModified) {
        return true;
    }

    QMessageBox::StandardButton result = QMessageBox::question(
        this,
        "Unsaved Changes",
        "The current playlist has unsaved changes. Do you want to save it?",
        QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel
    );

    switch (result) {
    case QMessageBox::Save:
        savePlaylist();
        return !m_playlistModified; // Return false if save failed
    case QMessageBox::Discard:
        return true;
    case QMessageBox::Cancel:
    default:
        return false;
    }
}

QStringList MainWindow::getSupportedAudioFiles()
{
    return QStringList() << "*.mp3" << "*.wav" << "*.flac" << "*.ogg"
                        << "*.m4a" << "*.aac" << "*.wma" << "*.mp4";
}

QStringList MainWindow::getAudioFileFilters()
{
    QStringList filters;
    filters << "Audio Files (" + getSupportedAudioFiles().join(" ") + ")";
    filters << "MP3 Files (*.mp3)";
    filters << "WAV Files (*.wav)";
    filters << "FLAC Files (*.flac)";
    filters << "OGG Files (*.ogg)";
    filters << "All Files (*)";
    return filters;
}

// Spotify slot implementations
void MainWindow::onSpotifyLoginClicked()
{
    if (m_spotifyAuth->isAuthenticated()) {
        // Logout
        m_spotifyAuth->logout();
    } else {
        // Start authentication
        ui->statusbar->showMessage("Starting Spotify authentication...", 3000);
        m_spotifyAuth->startAuthentication();
    }
}

void MainWindow::onSpotifySearchClicked()
{
    QString searchText = ui->spotifySearchEdit->text().trimmed();
    if (searchText.isEmpty()) {
        ui->statusbar->showMessage("💡 Please enter a search term (try: artist name, song title, or album)", 3000);
        return;
    }

    if (!m_spotifyAuth->isAuthenticated()) {
        ui->statusbar->showMessage("🔐 Please sign in to Spotify first to search for music", 3000);
        return;
    }

    // Clear previous results
    ui->spotifySearchResults->clear();
    qDeleteAll(m_currentSearchResults);
    m_currentSearchResults.clear();

    // Show loading message with animation
    ui->statusbar->showMessage("🔍 Searching Spotify for '" + searchText + "'...", 0);

    // Disable search while loading
    ui->spotifySearchButton->setEnabled(false);
    ui->spotifySearchEdit->setEnabled(false);

    // Perform actual Spotify search
    m_spotifyAPI->searchTracks(searchText, 10); // Limit to 10 results for UI
}

void MainWindow::onSpotifySearchTextChanged()
{
    // Hide search results when text changes
    if (ui->spotifySearchEdit->text().isEmpty()) {
        ui->spotifySearchResults->setVisible(false);
    }
}

void MainWindow::onSpotifySearchResultClicked(QListWidgetItem *item)
{
    if (!item) return;

    int row = ui->spotifySearchResults->row(item);
    if (row < 0 || row >= m_currentSearchResults.size()) return;

    SpotifyTrack *track = m_currentSearchResults[row];

    // Update track info display
    ui->trackTitleLabel->setText(track->name());
    ui->trackArtistLabel->setText(track->primaryArtist());
    ui->trackAlbumLabel->setText(track->albumName());

    // Load album art if available
    QUrl albumImageUrl = track->albumImageUrl("medium");
    if (!albumImageUrl.isEmpty()) {
        downloadAndSetAlbumArt(albumImageUrl);
    } else {
        // Set default album art
        QPixmap defaultArt(":/icons/default_album_art.svg");
        ui->albumArtLabel->setPixmap(defaultArt.scaled(150, 150, Qt::KeepAspectRatio, Qt::SmoothTransformation));
    }

    // Start Spotify preview playback
    QUrl previewUrl = track->previewUrl();
    if (!previewUrl.isEmpty()) {
        m_audioPlayer->setSpotifyPreview(previewUrl);
        m_audioPlayer->play();
        ui->statusbar->showMessage("Playing Spotify preview: " + track->displayText(), 5000);
    } else {
        ui->statusbar->showMessage("Selected: " + track->displayText() + " (No preview available)", 5000);
    }

    // Hide search results after selection
    ui->spotifySearchResults->setVisible(false);
    ui->spotifySearchEdit->clear();
}

// Spotify authentication slots
void MainWindow::onSpotifyAuthenticationSucceeded()
{
    ui->spotifyLoginButton->setText("Sign out");
    ui->spotifyLoginButton->setStyleSheet("QPushButton { background-color: #404040; color: white; font-weight: bold; }");
    ui->spotifySearchEdit->setEnabled(true);
    ui->spotifySearchButton->setEnabled(true);
    ui->spotifySearchEdit->setPlaceholderText("Search for songs, artists, or albums... (Press Enter or click search)");
    ui->statusbar->showMessage("🎵 Successfully connected to Spotify! You can now search for music.", 5000);

    // Show welcome message
    QMessageBox::information(this, "Spotify Connected",
        "🎵 Great! You're now connected to Spotify!\n\n"
        "✅ You can search for songs, artists, and albums\n"
        "✅ Click on search results to play 30-second previews\n"
        "✅ Album artwork will be automatically downloaded\n\n"
        "Try searching for your favorite artist or song!");
}

void MainWindow::onSpotifyAuthenticationFailed(const QString &error)
{
    ui->spotifyLoginButton->setText("Sign in to Spotify");
    ui->spotifyLoginButton->setStyleSheet("QPushButton { background-color: #1DB954; color: white; font-weight: bold; }");
    ui->spotifySearchEdit->setEnabled(false);
    ui->spotifySearchButton->setEnabled(false);
    ui->spotifySearchResults->setVisible(false);
    ui->statusbar->showMessage("Spotify authentication failed: " + error, 5000);
}

void MainWindow::onSpotifyLoggedOut()
{
    ui->spotifyLoginButton->setText("Sign in to Spotify");
    ui->spotifyLoginButton->setStyleSheet("QPushButton { background-color: #1DB954; color: white; font-weight: bold; }");
    ui->spotifySearchEdit->setEnabled(false);
    ui->spotifySearchButton->setEnabled(false);
    ui->spotifySearchResults->setVisible(false);
    ui->spotifySearchEdit->clear();

    // Clear search results
    ui->spotifySearchResults->clear();
    qDeleteAll(m_currentSearchResults);
    m_currentSearchResults.clear();

    ui->statusbar->showMessage("Signed out from Spotify", 3000);
}

// Spotify API slots
void MainWindow::onSpotifySearchTracksFinished(const QList<SpotifyTrack*> &tracks, int total, int offset)
{
    Q_UNUSED(offset)

    // Re-enable search controls
    ui->spotifySearchButton->setEnabled(true);
    ui->spotifySearchEdit->setEnabled(true);

    // Clear previous results
    ui->spotifySearchResults->clear();
    qDeleteAll(m_currentSearchResults);
    m_currentSearchResults.clear();

    // Store new results
    m_currentSearchResults = tracks;

    // Populate UI with enhanced display
    for (SpotifyTrack *track : tracks) {
        QString displayText = QString("🎵 %1 (%2)").arg(track->displayText(), track->durationText());
        ui->spotifySearchResults->addItem(displayText);
    }

    // Show results with better feedback
    if (!tracks.isEmpty()) {
        ui->spotifySearchResults->setVisible(true);
        ui->statusbar->showMessage(QString("✅ Found %1 tracks! Click any track to play preview.").arg(tracks.size()), 5000);
    } else {
        ui->spotifySearchResults->setVisible(false);
        ui->statusbar->showMessage("❌ No tracks found. Try a different search term.", 3000);
    }
}

void MainWindow::onSpotifyApiError(const QString &endpoint, int statusCode, const QString &message)
{
    Q_UNUSED(endpoint)

    // Re-enable search controls
    ui->spotifySearchButton->setEnabled(true);
    ui->spotifySearchEdit->setEnabled(true);

    ui->statusbar->showMessage(QString("⚠️ Spotify API error (%1): %2").arg(statusCode).arg(message), 5000);
}

void MainWindow::onSpotifyNetworkError(const QString &endpoint, const QString &error)
{
    Q_UNUSED(endpoint)

    // Re-enable search controls
    ui->spotifySearchButton->setEnabled(true);
    ui->spotifySearchEdit->setEnabled(true);

    ui->statusbar->showMessage("🌐 Network error: " + error + " (Check your internet connection)", 5000);
}

void MainWindow::setupSpotify()
{
    // Configure Spotify authentication with your credentials
    m_spotifyAuth->setClientId("922507ca05c54750b78a1e4aa27eb6df");
    m_spotifyAuth->setClientSecret("334e625e29c1468f8899f1a02694786a");
    m_spotifyAuth->setRedirectUri("http://127.0.0.1:3000/callback");

    // Set required scopes for the application
    QStringList scopes = {
        "user-read-private",
        "user-read-email",
        "playlist-read-private",
        "playlist-read-collaborative",
        "user-library-read",
        "streaming"
    };
    m_spotifyAuth->setScopes(scopes);

    // Initialize UI state
    ui->spotifySearchEdit->setEnabled(false);
    ui->spotifySearchButton->setEnabled(false);
    ui->spotifySearchResults->setVisible(false);

    // Set helpful tooltips
    ui->spotifyLoginButton->setToolTip("Connect to your Spotify account to search and play music");
    ui->spotifySearchEdit->setToolTip("Search for songs, artists, or albums on Spotify");
    ui->spotifySearchButton->setToolTip("Click to search Spotify");

    // Update placeholder text
    ui->spotifySearchEdit->setPlaceholderText("Sign in to Spotify first, then search for music...");
}

void MainWindow::downloadAndSetAlbumArt(const QUrl &imageUrl)
{
    // Create network request for album art
    QNetworkAccessManager *manager = new QNetworkAccessManager(this);
    QNetworkRequest request(imageUrl);
    QNetworkReply *reply = manager->get(request);

    // Store the reply for cleanup
    connect(reply, &QNetworkReply::finished, this, &MainWindow::onAlbumArtDownloaded);
    connect(reply, &QNetworkReply::finished, reply, &QNetworkReply::deleteLater);
    connect(reply, &QNetworkReply::finished, manager, &QNetworkAccessManager::deleteLater);
}

void MainWindow::onAlbumArtDownloaded()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    if (reply->error() == QNetworkReply::NoError) {
        QByteArray imageData = reply->readAll();
        QPixmap albumArt;

        if (albumArt.loadFromData(imageData)) {
            // Scale and set the album art
            QPixmap scaledArt = albumArt.scaled(150, 150, Qt::KeepAspectRatio, Qt::SmoothTransformation);
            ui->albumArtLabel->setPixmap(scaledArt);
        } else {
            // Failed to load image, use default
            QPixmap defaultArt(":/icons/default_album_art.svg");
            ui->albumArtLabel->setPixmap(defaultArt.scaled(150, 150, Qt::KeepAspectRatio, Qt::SmoothTransformation));
        }
    } else {
        // Network error, use default album art
        QPixmap defaultArt(":/icons/default_album_art.svg");
        ui->albumArtLabel->setPixmap(defaultArt.scaled(150, 150, Qt::KeepAspectRatio, Qt::SmoothTransformation));
    }
}
