;Audio Player NSIS Installer Script
;Created for Audio Player v1.0

;--------------------------------
;Include Modern UI
!include "MUI2.nsh"
!include "FileFunc.nsh"

;--------------------------------
;General
Name "Audio Player"
OutFile "AudioPlayer_Setup.exe"
Unicode True

;Default installation folder
InstallDir "$PROGRAMFILES64\Audio Player"

;Get installation folder from registry if available
InstallDirRegKey HKCU "Software\Audio Player" ""

;Request application privileges for Windows Vista/7/8/10/11
RequestExecutionLevel admin

;--------------------------------
;Variables
Var StartMenuFolder

;--------------------------------
;Interface Settings
!define MUI_ABORTWARNING
!define MUI_ICON "resources\icons\app_icon.ico"
!define MUI_UNICON "resources\icons\app_icon.ico"

;--------------------------------
;Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY

;Start Menu Folder Page Configuration
!define MUI_STARTMENUPAGE_REGISTRY_ROOT "HKCU" 
!define MUI_STARTMENUPAGE_REGISTRY_KEY "Software\Audio Player" 
!define MUI_STARTMENUPAGE_REGISTRY_VALUENAME "Start Menu Folder"
!insertmacro MUI_PAGE_STARTMENU Application $StartMenuFolder

!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

;--------------------------------
;Languages
!insertmacro MUI_LANGUAGE "English"

;--------------------------------
;Version Information
VIProductVersion "*******"
VIAddVersionKey "ProductName" "Audio Player"
VIAddVersionKey "Comments" "Modern Qt Audio Player with Playlist Support"
VIAddVersionKey "CompanyName" "Audio Player Inc."
VIAddVersionKey "LegalCopyright" "Copyright 2025"
VIAddVersionKey "FileDescription" "Audio Player Installer"
VIAddVersionKey "FileVersion" "*******"
VIAddVersionKey "ProductVersion" "*******"
VIAddVersionKey "InternalName" "AudioPlayer"
VIAddVersionKey "LegalTrademarks" ""
VIAddVersionKey "OriginalFilename" "AudioPlayer_Setup.exe"

;--------------------------------
;Installer Sections

Section "Audio Player (required)" SecMain
  SectionIn RO
  
  ; Set output path to the installation directory.
  SetOutPath $INSTDIR
  
  ; Put main executable
  File "build\release\AudioPlayer.exe"
  
  ; Put Qt DLLs (we'll need to copy these from Qt installation)
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6Core.dll"
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6Gui.dll"
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6Widgets.dll"
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6Multimedia.dll"
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6Network.dll"
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6Svg.dll"
  
  ; Put MinGW runtime DLLs
  File "C:\Qt\Tools\mingw1310_64\bin\libgcc_s_seh-1.dll"
  File "C:\Qt\Tools\mingw1310_64\bin\libstdc++-6.dll"
  File "C:\Qt\Tools\mingw1310_64\bin\libwinpthread-1.dll"
  
  ; Create platforms directory for Qt
  SetOutPath $INSTDIR\platforms
  File "C:\Qt\6.9.1\mingw_64\plugins\platforms\qwindows.dll"
  
  ; Create styles directory for Qt
  SetOutPath $INSTDIR\styles
  File "C:\Qt\6.9.1\mingw_64\plugins\styles\qwindowsvistastyle.dll"
  
  ; Create imageformats directory for Qt
  SetOutPath $INSTDIR\imageformats
  File "C:\Qt\6.9.1\mingw_64\plugins\imageformats\qico.dll"
  File "C:\Qt\6.9.1\mingw_64\plugins\imageformats\qjpeg.dll"
  File "C:\Qt\6.9.1\mingw_64\plugins\imageformats\qpng.dll"
  File "C:\Qt\6.9.1\mingw_64\plugins\imageformats\qsvg.dll"
  
  ; Create multimedia directory for Qt
  SetOutPath $INSTDIR\multimedia
  File "C:\Qt\6.9.1\mingw_64\plugins\multimedia\ffmpegmediaplugin.dll"
  File "C:\Qt\6.9.1\mingw_64\plugins\multimedia\windowsmediaplugin.dll"
  
  ; Reset output path
  SetOutPath $INSTDIR
  
  ; Store installation folder
  WriteRegStr HKCU "Software\Audio Player" "" $INSTDIR
  
  ; Create uninstaller
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; Add to Add/Remove Programs
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player" "DisplayName" "Audio Player"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player" "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player" "DisplayIcon" "$INSTDIR\AudioPlayer.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player" "Publisher" "Audio Player Inc."
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player" "DisplayVersion" "1.0.0"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player" "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player" "NoRepair" 1
  
  ; Get size of installation
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player" "EstimatedSize" "$0"

SectionEnd

Section "Desktop Shortcut" SecDesktop
  CreateShortcut "$DESKTOP\Audio Player.lnk" "$INSTDIR\AudioPlayer.exe"
SectionEnd

Section "Start Menu Shortcuts" SecStartMenu
  !insertmacro MUI_STARTMENU_WRITE_BEGIN Application
    
    ;Create shortcuts
    CreateDirectory "$SMPROGRAMS\$StartMenuFolder"
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\Audio Player.lnk" "$INSTDIR\AudioPlayer.exe"
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk" "$INSTDIR\Uninstall.exe"
  
  !insertmacro MUI_STARTMENU_WRITE_END
SectionEnd

Section "File Associations" SecFileAssoc
  ; Associate common audio file types
  WriteRegStr HKCR ".mp3" "" "AudioPlayer.AudioFile"
  WriteRegStr HKCR ".wav" "" "AudioPlayer.AudioFile"
  WriteRegStr HKCR ".flac" "" "AudioPlayer.AudioFile"
  WriteRegStr HKCR ".ogg" "" "AudioPlayer.AudioFile"
  WriteRegStr HKCR ".m4a" "" "AudioPlayer.AudioFile"
  WriteRegStr HKCR ".aac" "" "AudioPlayer.AudioFile"
  
  WriteRegStr HKCR "AudioPlayer.AudioFile" "" "Audio File"
  WriteRegStr HKCR "AudioPlayer.AudioFile\DefaultIcon" "" "$INSTDIR\AudioPlayer.exe,0"
  WriteRegStr HKCR "AudioPlayer.AudioFile\shell\open\command" "" '"$INSTDIR\AudioPlayer.exe" "%1"'
  WriteRegStr HKCR "AudioPlayer.AudioFile\shell\play\command" "" '"$INSTDIR\AudioPlayer.exe" "%1"'
SectionEnd

;--------------------------------
;Descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} "The main Audio Player application and required files."
  !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} "Create a shortcut on the desktop."
  !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} "Create shortcuts in the Start Menu."
  !insertmacro MUI_DESCRIPTION_TEXT ${SecFileAssoc} "Associate audio file types with Audio Player."
!insertmacro MUI_FUNCTION_DESCRIPTION_END

;--------------------------------
;Uninstaller Section

Section "Uninstall"

  ; Remove files and uninstaller
  Delete $INSTDIR\AudioPlayer.exe
  Delete $INSTDIR\*.dll
  Delete $INSTDIR\platforms\*.dll
  Delete $INSTDIR\styles\*.dll
  Delete $INSTDIR\imageformats\*.dll
  Delete $INSTDIR\multimedia\*.dll
  Delete $INSTDIR\Uninstall.exe

  ; Remove directories
  RMDir "$INSTDIR\platforms"
  RMDir "$INSTDIR\styles"
  RMDir "$INSTDIR\imageformats"
  RMDir "$INSTDIR\multimedia"
  RMDir "$INSTDIR"

  ; Remove shortcuts
  Delete "$DESKTOP\Audio Player.lnk"
  
  !insertmacro MUI_STARTMENU_GETFOLDER Application $StartMenuFolder
  Delete "$SMPROGRAMS\$StartMenuFolder\Audio Player.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk"
  RMDir "$SMPROGRAMS\$StartMenuFolder"
  
  ; Remove registry keys
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\Audio Player"
  DeleteRegKey HKCU "Software\Audio Player"
  
  ; Remove file associations
  DeleteRegKey HKCR ".mp3"
  DeleteRegKey HKCR ".wav"
  DeleteRegKey HKCR ".flac"
  DeleteRegKey HKCR ".ogg"
  DeleteRegKey HKCR ".m4a"
  DeleteRegKey HKCR ".aac"
  DeleteRegKey HKCR "AudioPlayer.AudioFile"

SectionEnd
