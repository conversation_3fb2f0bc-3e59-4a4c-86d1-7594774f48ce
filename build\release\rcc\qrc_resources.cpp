/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.9.1
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // dark_theme.qss
  0x0,0x0,0x5,0x17,
  0x0,
  0x0,0x17,0x49,0x78,0xda,0xb5,0x58,0x6d,0x4f,0xe3,0x38,0x10,0xfe,0xde,0x5f,0x61,
  0x81,0x4e,0x5a,0x50,0x73,0x24,0xa1,0x29,0x10,0x3e,0xc1,0xb2,0x48,0x27,0x1d,0x12,
  0x2b,0xba,0xb7,0x9f,0xdd,0xc4,0xb4,0x16,0x6e,0x9c,0x73,0x9c,0x5,0xf6,0xb4,0xff,
  0x7d,0xc7,0x71,0x9c,0x97,0xc6,0x49,0x5a,0xe,0x1c,0xa9,0x8a,0xed,0x76,0xfc,0xcc,
  0xcc,0x33,0xe3,0x99,0x9e,0x1c,0xa3,0x1b,0x2c,0x9e,0xd0,0x62,0x4d,0x36,0x4,0x3d,
  0xc8,0x57,0x46,0xb2,0x35,0x21,0x12,0x3d,0x72,0x81,0xae,0xf2,0x98,0x72,0x74,0xcf,
  0xf0,0x2b,0x11,0xe8,0xf8,0x64,0x32,0x39,0x39,0x46,0x77,0x98,0x26,0xe8,0x3b,0x4d,
  0x62,0xfe,0xac,0x96,0xbe,0xaa,0x79,0x39,0xfd,0x6f,0x82,0x60,0x2c,0x71,0xf4,0xb4,
  0x12,0x3c,0x4f,0x62,0x27,0xe2,0x8c,0x8b,0x10,0x1d,0xfa,0x4b,0xf5,0x5c,0x16,0xdb,
  0x66,0xed,0xb1,0x18,0x97,0x93,0x5f,0x93,0xc9,0xd7,0xef,0x34,0x5e,0xc1,0x91,0x6f,
  0xfa,0xbd,0x5a,0x7b,0xe4,0x89,0x74,0x1e,0xf1,0x86,0xb2,0xd7,0x10,0x1d,0x3c,0x90,
  0x15,0x27,0xe8,0xdb,0x5f,0x7,0x53,0x74,0x25,0x28,0x66,0x53,0x94,0xe1,0x24,0x73,
  0x32,0x22,0xa8,0x3e,0xf,0xb4,0xb8,0xce,0xa5,0xe4,0x49,0x56,0x68,0x70,0x9f,0x67,
  0x6b,0x3d,0xef,0x47,0x30,0x73,0xd5,0xa3,0x4f,0x5b,0x72,0x11,0x13,0x58,0xf4,0xd2,
  0x17,0x94,0x71,0x46,0x63,0x74,0x18,0x14,0xa3,0xb9,0xed,0x8,0x1c,0xd3,0x3c,0xb,
  0xd1,0x3c,0x7d,0xd1,0xeb,0x29,0x8e,0x63,0x9a,0xac,0x42,0x74,0xe,0xbf,0xf3,0xaa,
  0xe5,0xd,0x4d,0x9c,0x67,0x1a,0xcb,0x35,0x6c,0xb8,0x66,0xb1,0x50,0xe8,0x99,0xd0,
  0xd5,0x5a,0x86,0x28,0x70,0x5d,0x6d,0xa6,0x1a,0x68,0xb8,0xe6,0x3f,0xc0,0x25,0xbd,
  0x70,0x3,0x57,0x3d,0x2d,0x3c,0x66,0xeb,0xac,0x18,0x1d,0x81,0xa9,0x20,0x59,0x46,
  0xe2,0x7e,0x91,0xa7,0x81,0x7a,0xac,0x22,0x4f,0x8b,0xd1,0x11,0x19,0xd3,0xc,0x2f,
  0xd9,0x90,0x4c,0x1f,0xab,0xc7,0x2e,0x13,0xab,0xa7,0xed,0xf2,0x79,0x31,0x8c,0xb,
  0x3f,0x83,0x8d,0x4,0x67,0x95,0x2b,0x3f,0x29,0x9e,0x4e,0xd1,0x3d,0xce,0x33,0x32,
  0x45,0x44,0x46,0x7f,0x1e,0x6d,0xb9,0xf7,0x30,0x85,0x6f,0x14,0xfb,0x7a,0x3e,0x6d,
  0x6d,0x66,0x92,0xa7,0xb6,0xf5,0x84,0xbc,0x48,0xdb,0x3a,0x58,0xec,0x7,0xe5,0x79,
  0x66,0xdb,0xdb,0xe4,0x92,0xbc,0x85,0x52,0xfe,0x4e,0x94,0xa,0xdc,0x3f,0xba,0x94,
  0xda,0x36,0xff,0xb6,0xb2,0x9a,0x32,0x7d,0x2a,0xdb,0x76,0x6b,0xc5,0x6d,0xbb,0x6d,
  0xf5,0x6d,0xdf,0xa8,0x8d,0x30,0x46,0x57,0xdf,0xbb,0x98,0xdf,0x9e,0x5a,0x79,0xe0,
  0x5d,0x9c,0xcd,0x6f,0xfc,0x51,0xe5,0x4a,0xfa,0xf6,0xaa,0x67,0xdd,0x6f,0x28,0x68,
  0xdd,0xdf,0x52,0xd1,0xfa,0x9d,0x86,0x92,0xa3,0x21,0xe4,0x5,0xf3,0xe0,0xb3,0x3d,
  0x2a,0xdd,0x9b,0xd9,0xd9,0x95,0x67,0xa8,0xfd,0x0,0xc,0x20,0x42,0x67,0x27,0xfd,
  0x1e,0x86,0x20,0xb,0x8c,0x8,0x96,0x14,0xf4,0x27,0x30,0x1f,0x33,0x73,0x4e,0x37,
  0x1b,0x5d,0x14,0x43,0x9f,0xb3,0x2e,0x93,0xc8,0xb9,0x49,0x2d,0x35,0xae,0x10,0xfd,
  0xcb,0x68,0x42,0xb0,0x58,0x29,0x62,0x91,0x44,0x7e,0x7a,0xf1,0x42,0x77,0x8a,0x5e,
  0x8b,0xcf,0x17,0xbf,0x78,0xf7,0x43,0xf,0xf2,0x27,0x18,0x32,0x74,0xd,0x65,0xcb,
  0xb9,0x67,0x72,0xc2,0x51,0x99,0xc8,0x40,0x10,0x4d,0x34,0x87,0x5d,0x2b,0x6f,0x67,
  0x86,0xa5,0x46,0xa7,0x35,0x4e,0x62,0x66,0xd3,0xe9,0xff,0x60,0xf4,0xaf,0x82,0xdb,
  0xa0,0xc6,0xa8,0xb9,0x75,0xd4,0x9b,0xba,0xd,0xc1,0xd4,0x76,0x99,0x87,0xbd,0xca,
  0x58,0x46,0x27,0xa7,0x5f,0xa9,0x8b,0x51,0xa5,0x7a,0xd8,0xbf,0xbf,0x6a,0xf3,0xd9,
  0x75,0x70,0x3b,0xaf,0x55,0xd3,0xaa,0x1e,0xd9,0xe3,0xe6,0xcb,0xf9,0xf9,0x97,0x60,
  0xc,0x59,0x1f,0x65,0xf7,0xc7,0xa6,0xcd,0x58,0x63,0xd3,0x5c,0x3f,0x1a,0x23,0x7b,
  0x85,0x2d,0xcb,0x97,0x4e,0x8a,0x57,0xef,0x4c,0x6,0xed,0xfc,0x6,0xaa,0x2,0xe5,
  0x0,0x19,0x1a,0x11,0xda,0x8d,0x1c,0x3b,0x9b,0x21,0x5e,0xff,0xa6,0x99,0x44,0xff,
  0x50,0xf2,0xac,0x2f,0x21,0x6,0x53,0x7d,0xf7,0xa8,0x8d,0x62,0x7d,0x8f,0x6b,0x75,
  0xef,0xc2,0x2,0x33,0x49,0x44,0x82,0x25,0x71,0x46,0x6e,0x99,0x8c,0x30,0x12,0x49,
  0xca,0x13,0x67,0x24,0x7,0xf3,0x5c,0x2a,0x43,0x87,0x28,0xe1,0x9,0xd1,0x7e,0x32,
  0x9a,0x84,0x21,0x95,0x64,0x53,0xea,0x53,0xdd,0x3f,0x9e,0xbb,0x65,0xa4,0x25,0x87,
  0x94,0xb8,0x69,0xa9,0x32,0x2b,0x46,0x7f,0x11,0xd8,0x3e,0x21,0xd4,0x60,0x7,0xab,
  0x87,0x6,0xe2,0x1d,0x4,0xee,0x58,0x32,0xd,0x40,0x19,0x13,0xd1,0x88,0x39,0x45,
  0xa,0xbc,0x24,0x4c,0xe7,0xf0,0xe2,0xb5,0xf7,0x77,0x52,0x40,0x79,0x9a,0x62,0x1,
  0x84,0x1e,0x50,0x46,0x89,0x38,0xc4,0x6c,0x99,0x6f,0xae,0x84,0x6c,0x9,0xdc,0xab,
  0x76,0xb0,0xdc,0x3,0x56,0xae,0x54,0xae,0xf5,0x4d,0x7e,0xd3,0x8,0x0,0x6c,0xf4,
  0xb4,0xa0,0x92,0x91,0x26,0x86,0xa2,0x5e,0xcd,0xe8,0x4f,0xd2,0x4c,0x9e,0xad,0x22,
  0x76,0xc9,0x59,0xdc,0x5f,0xc0,0xeb,0x3c,0xeb,0xa8,0x28,0x45,0x5e,0xd0,0xce,0xbe,
  0xe,0x23,0x8f,0xb2,0x49,0xb1,0x72,0x59,0x68,0xc1,0xf5,0xba,0x84,0xeb,0xdc,0xc1,
  0x8c,0xae,0x20,0x61,0x47,0x60,0x4c,0x22,0x3a,0xc0,0xc1,0x74,0xe0,0xda,0x3e,0xe4,
  0x33,0x23,0xc9,0x40,0x8c,0x8a,0xd1,0x85,0x78,0xfe,0x81,0x8,0x95,0x87,0xfb,0x0,
  0xfa,0xdb,0x0,0x9b,0x37,0xbd,0xfe,0xa2,0x6a,0xe0,0x42,0x44,0x21,0x7d,0x52,0xb,
  0xf0,0x8f,0x31,0x6d,0x5a,0xe6,0xbc,0x51,0xbb,0xee,0xc1,0x88,0x8a,0x7f,0x81,0xbe,
  0x79,0xeb,0xd3,0xa2,0x5c,0xa8,0x50,0x59,0xd0,0x8d,0xa6,0xe0,0xb4,0xb2,0x1f,0x7,
  0xad,0xab,0xe5,0x26,0x8e,0xaa,0x35,0x84,0x9e,0x1,0x42,0x4,0x67,0xd0,0x1a,0x1e,
  0xdc,0xf1,0x4,0x47,0x1c,0xde,0x36,0x3c,0xe1,0x10,0x7f,0x11,0xb9,0x1c,0x37,0x78,
  0x8b,0x11,0x75,0xe7,0x36,0xb,0xea,0xcb,0xe0,0x8e,0x24,0x39,0xba,0xc6,0x42,0x77,
  0xc7,0x30,0x51,0xef,0xfb,0xb4,0x55,0x96,0xdc,0xd9,0xc,0xe8,0x41,0x6b,0x55,0xd1,
  0x5a,0x1e,0xdc,0x4a,0xd7,0x3b,0xa4,0x9e,0x4a,0xd0,0x5c,0x35,0xa9,0xfe,0xc8,0xd5,
  0xd7,0x3e,0xe5,0x1d,0x52,0x76,0x5b,0xde,0x78,0x41,0x5d,0x37,0x7,0xc6,0xf0,0xc6,
  0xe8,0x1f,0x7a,0xe3,0x56,0x56,0x6a,0x19,0xc2,0x7e,0x35,0xaa,0x6e,0xdf,0x9f,0xed,
  0x62,0xc8,0x77,0xb5,0x22,0x54,0x54,0x4,0x1c,0x8b,0x25,0x37,0xe4,0x33,0xf5,0x8c,
  0x37,0x70,0x3,0x34,0x95,0x37,0xb5,0x2f,0x60,0xac,0xfa,0x4b,0xd5,0x9b,0x48,0x2c,
  0xf3,0xac,0x22,0xb8,0x9e,0xee,0x4b,0x71,0x9d,0xe8,0x87,0xf9,0xdd,0xc,0xb5,0x8e,
  0xc1,0x15,0x90,0x94,0x51,0x29,0x49,0x9,0xa3,0x9c,0x98,0x22,0x77,0xe0,0x9e,0x2f,
  0x8f,0x2a,0x8a,0xcf,0xad,0x5f,0x75,0x8b,0xcf,0x32,0xbe,0x4f,0x7b,0xfc,0xe7,0x55,
  0xc5,0x7f,0x57,0xd2,0x60,0xa9,0xd0,0xf8,0x17,0x46,0xa9,0x12,0x9,0xce,0x98,0xb2,
  0x69,0xd9,0xf3,0x15,0x73,0x15,0x8,0x20,0x44,0xd2,0x8,0xb3,0xdd,0xfe,0x46,0x30,
  0xfd,0x4b,0x5f,0xdc,0xce,0x2b,0xb8,0xd5,0x1,0x6,0xef,0xf8,0x41,0xe6,0xdf,0x97,
  0xa1,0xc0,0x50,0x29,0xd1,0xf0,0xcc,0x77,0xc7,0x4f,0xdb,0xc3,0x4c,0x4d,0x29,0xc0,
  0x6,0xa7,0x28,0x50,0x8d,0x9c,0x69,0x6b,0x5b,0xf5,0x12,0xad,0xed,0xad,0x4a,0x49,
  0x57,0xb5,0xdb,0xbd,0x45,0x5d,0xeb,0xd6,0xa2,0x6,0x9a,0x11,0xab,0x3,0xaa,0x18,
  0x7b,0x83,0x7,0x76,0x39,0x6b,0x57,0x1f,0x94,0x44,0x18,0x72,0xc1,0x68,0x7f,0xba,
  0xb3,0x13,0x6a,0x49,0x3d,0x6e,0xe8,0xfd,0xcf,0x62,0xc4,0x11,0x10,0x19,0xb,0xce,
  0x99,0xa4,0xa9,0xe,0xb,0x35,0x59,0xd0,0x74,0xc0,0x15,0x81,0x7a,0xfa,0xaf,0x49,
  0x4b,0xba,0x1f,0xb0,0xe8,0xac,0x93,0xee,0xe7,0xad,0x42,0xa6,0x59,0x23,0xfc,0x9a,
  0xfc,0x6,0x37,0x29,0x2f,0x35,
    // play.svg
  0x0,0x0,0x0,0xf8,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x38,0x20,0x35,0x56,0x31,0x39,0x4c,0x31,0x39,0x20,0x31,0x32,0x4c,0x38,
  0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,
  0x66,0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,
  0x68,0x3d,0x22,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,
  0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // pause.svg
  0x0,0x0,0x1,0x5,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x36,0x22,0x20,0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,
  0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,
  0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,0x22,0x31,0x34,0x22,0x20,
  0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x34,0x22,0x20,
  0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,
  0x76,0x67,0x3e,0xa,
    // previous.svg
  0x0,0x0,0x0,0xfa,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x32,0x30,0x20,0x35,0x56,0x31,0x39,0x4c,0x39,0x20,0x31,0x32,0x4c,0x32,
  0x30,0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,
  0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,
  0x3d,0x22,0x34,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x33,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x34,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // volume.svg
  0x0,0x0,0x1,0xe4,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x39,0x2e,0x30,0x37,0x20,0x34,
  0x2e,0x39,0x33,0x43,0x32,0x30,0x2e,0x39,0x20,0x36,0x2e,0x37,0x36,0x20,0x32,0x31,
  0x2e,0x39,0x20,0x39,0x2e,0x33,0x20,0x32,0x31,0x2e,0x39,0x20,0x31,0x32,0x43,0x32,
  0x31,0x2e,0x39,0x20,0x31,0x34,0x2e,0x37,0x20,0x32,0x30,0x2e,0x39,0x20,0x31,0x37,
  0x2e,0x32,0x34,0x20,0x31,0x39,0x2e,0x30,0x37,0x20,0x31,0x39,0x2e,0x30,0x37,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,
  0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x35,0x2e,0x35,0x34,0x20,0x38,0x2e,
  0x34,0x36,0x43,0x31,0x36,0x2e,0x34,0x20,0x39,0x2e,0x33,0x32,0x20,0x31,0x36,0x2e,
  0x39,0x20,0x31,0x30,0x2e,0x36,0x20,0x31,0x36,0x2e,0x39,0x20,0x31,0x32,0x43,0x31,
  0x36,0x2e,0x39,0x20,0x31,0x33,0x2e,0x34,0x20,0x31,0x36,0x2e,0x34,0x20,0x31,0x34,
  0x2e,0x36,0x38,0x20,0x31,0x35,0x2e,0x35,0x34,0x20,0x31,0x35,0x2e,0x35,0x34,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // next.svg
  0x0,0x0,0x0,0xfa,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x34,0x20,0x35,0x56,0x31,0x39,0x4c,0x31,0x35,0x20,0x31,0x32,0x4c,0x34,
  0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x31,0x37,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x33,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x34,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // default_album_art.svg
  0x0,0x0,0x4,0x97,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x30,0x30,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,
  0x30,0x30,0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,
  0x20,0x32,0x30,0x30,0x20,0x32,0x30,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x6e,0x6f,0x6e,0x65,0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,
  0x70,0x3a,0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,
  0x30,0x30,0x30,0x2f,0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,
  0x20,0x42,0x61,0x63,0x6b,0x67,0x72,0x6f,0x75,0x6e,0x64,0x20,0x2d,0x2d,0x3e,0xa,
  0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x30,0x30,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x30,0x30,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x34,0x30,0x34,0x30,0x34,0x30,0x22,0x20,
  0x72,0x78,0x3d,0x22,0x38,0x22,0x2f,0x3e,0xa,0x20,0x20,0xa,0x20,0x20,0x3c,0x21,
  0x2d,0x2d,0x20,0x56,0x69,0x6e,0x79,0x6c,0x20,0x72,0x65,0x63,0x6f,0x72,0x64,0x20,
  0x64,0x65,0x73,0x69,0x67,0x6e,0x20,0x2d,0x2d,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,
  0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x38,0x30,0x22,0x20,0x66,0x69,
  0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,
  0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,
  0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x37,0x30,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x31,0x61,0x31,0x61,0x31,0x61,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,
  0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,
  0x3d,0x22,0x36,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,
  0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,
  0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,
  0x30,0x22,0x20,0x72,0x3d,0x22,0x35,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x23,0x31,0x61,0x31,0x61,0x31,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,
  0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x34,0x30,0x22,0x20,0x66,0x69,
  0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,
  0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,
  0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x33,0x30,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x31,0x61,0x31,0x61,0x31,0x61,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,
  0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,
  0x3d,0x22,0x32,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x32,0x61,0x32,
  0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,
  0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,
  0x30,0x22,0x20,0x72,0x3d,0x22,0x31,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x23,0x34,0x30,0x34,0x30,0x34,0x30,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,
  0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x34,0x22,0x20,0x66,0x69,0x6c,
  0x6c,0x3d,0x22,0x23,0x36,0x36,0x36,0x36,0x36,0x36,0x22,0x2f,0x3e,0xa,0x20,0x20,
  0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0x4d,0x75,0x73,0x69,0x63,0x20,0x6e,0x6f,
  0x74,0x65,0x20,0x69,0x63,0x6f,0x6e,0x20,0x2d,0x2d,0x3e,0xa,0x20,0x20,0x3c,0x67,
  0x20,0x74,0x72,0x61,0x6e,0x73,0x66,0x6f,0x72,0x6d,0x3d,0x22,0x74,0x72,0x61,0x6e,
  0x73,0x6c,0x61,0x74,0x65,0x28,0x38,0x35,0x2c,0x20,0x37,0x30,0x29,0x22,0x3e,0xa,
  0x20,0x20,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x38,0x20,
  0x31,0x38,0x56,0x36,0x4c,0x32,0x30,0x20,0x34,0x56,0x31,0x36,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x38,0x38,0x38,0x38,0x38,0x38,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,
  0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,
  0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,
  0x20,0x63,0x78,0x3d,0x22,0x36,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x38,0x22,0x20,
  0x72,0x3d,0x22,0x32,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x38,0x38,0x38,
  0x38,0x38,0x38,0x22,0x2f,0x3e,0xa,0x20,0x20,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,
  0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x38,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,
  0x36,0x22,0x20,0x72,0x3d,0x22,0x32,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,
  0x38,0x38,0x38,0x38,0x38,0x38,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x2f,0x67,0x3e,
  0xa,0x20,0x20,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0x44,0x65,0x63,0x6f,0x72,
  0x61,0x74,0x69,0x76,0x65,0x20,0x74,0x65,0x78,0x74,0x20,0x2d,0x2d,0x3e,0xa,0x20,
  0x20,0x3c,0x74,0x65,0x78,0x74,0x20,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x79,
  0x3d,0x22,0x31,0x36,0x30,0x22,0x20,0x74,0x65,0x78,0x74,0x2d,0x61,0x6e,0x63,0x68,
  0x6f,0x72,0x3d,0x22,0x6d,0x69,0x64,0x64,0x6c,0x65,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x36,0x36,0x36,0x36,0x36,0x36,0x22,0x20,0x66,0x6f,0x6e,0x74,0x2d,
  0x66,0x61,0x6d,0x69,0x6c,0x79,0x3d,0x22,0x41,0x72,0x69,0x61,0x6c,0x2c,0x20,0x73,
  0x61,0x6e,0x73,0x2d,0x73,0x65,0x72,0x69,0x66,0x22,0x20,0x66,0x6f,0x6e,0x74,0x2d,
  0x73,0x69,0x7a,0x65,0x3d,0x22,0x31,0x32,0x22,0x20,0x66,0x6f,0x6e,0x74,0x2d,0x77,
  0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x62,0x6f,0x6c,0x64,0x22,0x3e,0x4e,0x4f,0x20,
  0x41,0x52,0x54,0x57,0x4f,0x52,0x4b,0x3c,0x2f,0x74,0x65,0x78,0x74,0x3e,0xa,0x3c,
  0x2f,0x73,0x76,0x67,0x3e,0xa,
    // add.svg
  0x0,0x0,0x0,0xe6,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x32,0x20,0x35,0x56,0x31,0x39,0x4d,0x35,0x20,0x31,0x32,0x48,0x31,
  0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,
  0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,0xa,
    // app_icon.svg
  0x0,0x0,0x0,0xf4,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x34,0x38,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x34,0x38,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x34,
  0x38,0x20,0x34,0x38,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,
  0x63,0x78,0x3d,0x22,0x32,0x34,0x22,0x20,0x63,0x79,0x3d,0x22,0x32,0x34,0x22,0x20,
  0x72,0x3d,0x22,0x32,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x32,0x31,
  0x39,0x36,0x46,0x33,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,
  0x64,0x3d,0x22,0x4d,0x31,0x38,0x20,0x31,0x34,0x56,0x33,0x34,0x4c,0x33,0x34,0x20,
  0x32,0x34,0x4c,0x31,0x38,0x20,0x31,0x34,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,
  0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // volume_mute.svg
  0x0,0x0,0x1,0x23,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x32,0x33,0x20,0x39,0x4c,0x31,0x37,
  0x20,0x31,0x35,0x4d,0x31,0x37,0x20,0x39,0x4c,0x32,0x33,0x20,0x31,0x35,0x22,0x20,
  0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,
  0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,
  0x3e,0xa,
    // remove.svg
  0x0,0x0,0x0,0xde,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x35,0x20,0x31,0x32,0x48,0x31,0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,
  0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // stop.svg
  0x0,0x0,0x0,0xca,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x36,0x22,0x20,0x79,0x3d,0x22,0x36,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x31,0x32,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x32,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // styles
  0x0,0x6,
  0x7,0xac,0x2,0xc3,
  0x0,0x73,
  0x0,0x74,0x0,0x79,0x0,0x6c,0x0,0x65,0x0,0x73,
    // dark_theme.qss
  0x0,0xe,
  0xd,0x16,0x97,0xc3,
  0x0,0x64,
  0x0,0x61,0x0,0x72,0x0,0x6b,0x0,0x5f,0x0,0x74,0x0,0x68,0x0,0x65,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x71,0x0,0x73,0x0,0x73,
    // play.svg
  0x0,0x8,
  0x2,0x8c,0x54,0x27,
  0x0,0x70,
  0x0,0x6c,0x0,0x61,0x0,0x79,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // pause.svg
  0x0,0x9,
  0xc,0x98,0xb7,0xc7,
  0x0,0x70,
  0x0,0x61,0x0,0x75,0x0,0x73,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // previous.svg
  0x0,0xc,
  0x8,0x37,0xc0,0xc7,
  0x0,0x70,
  0x0,0x72,0x0,0x65,0x0,0x76,0x0,0x69,0x0,0x6f,0x0,0x75,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume.svg
  0x0,0xa,
  0xc,0x3b,0xf6,0xa7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // next.svg
  0x0,0x8,
  0xc,0xf7,0x54,0x47,
  0x0,0x6e,
  0x0,0x65,0x0,0x78,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // default_album_art.svg
  0x0,0x15,
  0xa,0xf6,0x60,0xa7,
  0x0,0x64,
  0x0,0x65,0x0,0x66,0x0,0x61,0x0,0x75,0x0,0x6c,0x0,0x74,0x0,0x5f,0x0,0x61,0x0,0x6c,0x0,0x62,0x0,0x75,0x0,0x6d,0x0,0x5f,0x0,0x61,0x0,0x72,0x0,0x74,
  0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // add.svg
  0x0,0x7,
  0x7,0xa7,0x5a,0x7,
  0x0,0x61,
  0x0,0x64,0x0,0x64,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // app_icon.svg
  0x0,0xc,
  0x7,0x6f,0x91,0x27,
  0x0,0x61,
  0x0,0x70,0x0,0x70,0x0,0x5f,0x0,0x69,0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume_mute.svg
  0x0,0xf,
  0xb,0x22,0xfb,0xc7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x5f,0x0,0x6d,0x0,0x75,0x0,0x74,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // remove.svg
  0x0,0xa,
  0x6,0xcb,0x42,0x47,
  0x0,0x72,
  0x0,0x65,0x0,0x6d,0x0,0x6f,0x0,0x76,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // stop.svg
  0x0,0x8,
  0xb,0x63,0x55,0x87,
  0x0,0x73,
  0x0,0x74,0x0,0x6f,0x0,0x70,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0xb,0x0,0x0,0x0,0x4,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles
  0x0,0x0,0x0,0x10,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles/dark_theme.qss
  0x0,0x0,0x0,0x22,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xd9,0xa6,0x9d,0x4a,
  // :/icons/play.svg
  0x0,0x0,0x0,0x44,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x5,0x1b,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x6a,0xb1,
  // :/icons/remove.svg
  0x0,0x0,0x1,0x46,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x12,0xa8,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x1a,0x8,
  // :/icons/app_icon.svg
  0x0,0x0,0x1,0x4,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x10,0x89,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x2f,0xeb,
  // :/icons/add.svg
  0x0,0x0,0x0,0xf0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xf,0x9f,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x5,0xd4,
  // :/icons/previous.svg
  0x0,0x0,0x0,0x72,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x7,0x20,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xbb,0x98,
  // :/icons/default_album_art.svg
  0x0,0x0,0x0,0xc0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xb,0x4,
0x0,0x0,0x1,0x97,0xd9,0x9d,0x64,0x35,
  // :/icons/volume_mute.svg
  0x0,0x0,0x1,0x22,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x11,0x81,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xf2,0x4e,
  // :/icons/stop.svg
  0x0,0x0,0x1,0x60,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x13,0x8a,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x93,0x2b,
  // :/icons/volume.svg
  0x0,0x0,0x0,0x90,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x8,0x1e,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xdb,0xe5,
  // :/icons/pause.svg
  0x0,0x0,0x0,0x5a,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x6,0x17,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x7f,0x6f,
  // :/icons/next.svg
  0x0,0x0,0x0,0xaa,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xa,0x6,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xa7,0x9c,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
