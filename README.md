# Audio Player

A modern, feature-rich audio player built with Qt 6 and C++. This application provides a sleek dark-themed interface with comprehensive playlist management and audio playback capabilities.

## Features

### Core Functionality
- **Audio Playback**: Support for MP3, WAV, FLAC, OGG, M4A, AAC, WMA, and MP4 audio formats
- **Playback Controls**: Play, pause, stop, next, previous with intuitive button controls
- **Volume Control**: Adjustable volume slider with mute functionality
- **Progress Control**: Seekable progress bar showing current playback position
- **Track Information**: Display of current track title and artist information

### Playlist Management
- **File Management**: Easy file browser integration for adding audio files
- **Playlist Operations**: Create, save, and load playlists in M3U format
- **Drag & Drop**: Support for drag-and-drop file addition and playlist reordering
- **Multiple Playlists**: Create and manage multiple playlists
- **Track Removal**: Remove individual tracks from playlists

### User Interface
- **Dark Theme**: Modern dark-themed interface with custom styling
- **Responsive Layout**: Adaptive layout that works on different screen sizes
- **SVG Icons**: Professional vector icons throughout the interface
- **Keyboard Shortcuts**: Comprehensive keyboard shortcuts for all major functions
- **Status Updates**: Real-time status information and error handling

### Technical Features
- **Qt 6 Integration**: Built with modern Qt 6 framework
- **QMediaPlayer**: Uses Qt's robust multimedia framework
- **Settings Persistence**: Saves user preferences and window state
- **Error Handling**: Comprehensive error handling for unsupported formats

## Requirements

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 20.04+)
- **Qt Version**: Qt 6.2 or later
- **Compiler**: 
  - Windows: MSVC 2019 or later, MinGW 8.1+
  - macOS: Xcode 12+
  - Linux: GCC 9+ or Clang 10+
- **CMake**: Version 3.16 or later

### Qt Modules Required
- Qt6::Core
- Qt6::Widgets
- Qt6::Multimedia
- Qt6::Svg

## Building the Application

### Using Qt Creator (Recommended)

1. **Install Qt Creator** with Qt 6.2+ and the required modules
2. **Open the project**:
   - Launch Qt Creator
   - File → Open File or Project
   - Select `CMakeLists.txt` from the project root
3. **Configure the project**:
   - Select your Qt 6 kit
   - Choose Debug or Release build configuration
4. **Build and run**:
   - Press `Ctrl+B` to build
   - Press `Ctrl+R` to run

### Command Line Build

#### Windows (with Qt installed)
```bash
mkdir build
cd build
cmake .. -DCMAKE_PREFIX_PATH="C:/Qt/6.5.0/msvc2019_64"
cmake --build . --config Release
```

#### macOS
```bash
mkdir build
cd build
cmake .. -DCMAKE_PREFIX_PATH="/usr/local/Qt-6.5.0"
make -j4
```

#### Linux
```bash
mkdir build
cd build
cmake .. -DCMAKE_PREFIX_PATH="/opt/Qt/6.5.0/gcc_64"
make -j4
```

## Usage

### Getting Started
1. **Launch the application**
2. **Add audio files**:
   - Click "Add Files" button or use `Ctrl+O`
   - Drag and drop audio files directly into the application
3. **Control playback**:
   - Use the central play/pause button or press `Space`
   - Navigate tracks with previous/next buttons or `Ctrl+Left/Right`
   - Adjust volume with the volume slider
4. **Manage playlists**:
   - Create new playlists with `Ctrl+N`
   - Save playlists with `Ctrl+S`
   - Load existing playlists with `Ctrl+Shift+O`

### Keyboard Shortcuts
- `Space` - Play/Pause
- `Ctrl+O` - Open Files
- `Ctrl+N` - New Playlist
- `Ctrl+S` - Save Playlist
- `Ctrl+Shift+O` - Open Playlist
- `Ctrl+Shift+S` - Save Playlist As
- `Ctrl+Left` - Previous Track
- `Ctrl+Right` - Next Track
- `Ctrl+.` - Stop
- `Ctrl+Q` - Exit

### Supported Audio Formats
- **MP3** - MPEG Audio Layer 3
- **WAV** - Waveform Audio File Format
- **FLAC** - Free Lossless Audio Codec
- **OGG** - Ogg Vorbis
- **M4A** - MPEG-4 Audio
- **AAC** - Advanced Audio Coding
- **WMA** - Windows Media Audio
- **MP4** - MPEG-4 Audio (audio track)

## Project Structure

```
AudioPlayer/
├── CMakeLists.txt          # CMake build configuration
├── README.md               # This file
├── src/                    # Source files
│   ├── main.cpp           # Application entry point
│   ├── mainwindow.cpp     # Main window implementation
│   ├── audioplayer.cpp    # Audio player logic
│   ├── playlist.cpp       # Playlist management
│   └── playlistmodel.cpp  # Playlist model for UI
├── include/                # Header files
│   ├── mainwindow.h
│   ├── audioplayer.h
│   ├── playlist.h
│   └── playlistmodel.h
├── ui/                     # Qt Designer UI files
│   └── mainwindow.ui      # Main window layout
└── resources/              # Application resources
    ├── resources.qrc      # Resource file
    ├── icons/             # SVG icons
    └── styles/            # Stylesheets
        └── dark_theme.qss # Dark theme styling
```

## Architecture

### Core Components

1. **AudioPlayer**: Wraps Qt's QMediaPlayer and QAudioOutput for audio playback
2. **Playlist**: Manages playlist data and operations (add, remove, reorder)
3. **PlaylistModel**: Qt model for displaying playlist in the UI
4. **MainWindow**: Main application window coordinating all components

### Design Patterns
- **Model-View**: Playlist data separated from UI presentation
- **Observer**: Signal-slot connections for component communication
- **Facade**: AudioPlayer simplifies QMediaPlayer complexity

## Troubleshooting

### Common Issues

**Application won't start**
- Ensure Qt 6 runtime libraries are available
- Check that all required Qt modules are installed

**Audio files won't play**
- Verify the audio format is supported
- Check system audio codecs are installed
- Ensure audio device is not in use by another application

**Build errors**
- Verify Qt 6 installation and CMake configuration
- Check that all required Qt modules are available
- Ensure compiler meets minimum requirements

### Getting Help
- Check the Qt documentation for multimedia issues
- Verify audio codec availability on your system
- Ensure proper Qt 6 installation with all required modules

## License

This project is provided as-is for educational and demonstration purposes.

## Contributing

This is a demonstration project. For production use, consider:
- Adding unit tests
- Implementing metadata extraction
- Adding equalizer functionality
- Supporting additional audio formats
- Adding shuffle and repeat modes
- Implementing audio visualization
