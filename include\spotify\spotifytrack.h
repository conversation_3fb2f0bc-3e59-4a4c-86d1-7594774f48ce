#ifndef SPOTIFYTRACK_H
#define SPOTIFYTRACK_H

#include <QObject>
#include <QString>
#include <QUrl>
#include <QJsonObject>
#include <QStringList>

class SpotifyTrack : public QObject
{
    Q_OBJECT

public:
    explicit SpotifyTrack(QObject *parent = nullptr);
    explicit SpotifyTrack(const QJsonObject &trackData, QObject *parent = nullptr);

    // Basic track information
    QString id() const;
    QString name() const;
    QString uri() const;
    QUrl externalUrl() const;
    QUrl previewUrl() const;
    int durationMs() const;
    int popularity() const;
    bool isExplicit() const;
    bool isPlayable() const;

    // Artist information
    QStringList artistNames() const;
    QStringList artistIds() const;
    QString primaryArtist() const;

    // Album information
    QString albumName() const;
    QString albumId() const;
    QUrl albumImageUrl(const QString &size = "medium") const; // small, medium, large
    QString releaseDate() const;

    // Audio features
    QString key() const;
    QString mode() const;
    float tempo() const;
    float energy() const;
    float danceability() const;
    float valence() const;

    // Utility methods
    QString displayText() const;
    QString durationText() const;
    bool isValid() const;
    QJsonObject toJson() const;

    // Static factory methods
    static SpotifyTrack* fromJson(const QJsonObject &json, QObject *parent = nullptr);
    static QList<SpotifyTrack*> fromJsonArray(const QJsonArray &array, QObject *parent = nullptr);

public slots:
    void updateFromJson(const QJsonObject &trackData);
    void setPreviewUrl(const QUrl &url);  // Manual override for demo/testing

signals:
    void trackUpdated();

private:
    void parseTrackData(const QJsonObject &trackData);
    void parseArtists(const QJsonArray &artists);
    void parseAlbum(const QJsonObject &album);
    QString formatDuration(int milliseconds) const;

    // Core track data
    QString m_id;
    QString m_name;
    QString m_uri;
    QUrl m_externalUrl;
    QUrl m_previewUrl;
    int m_durationMs;
    int m_popularity;
    bool m_explicit;
    bool m_playable;

    // Artist data
    QStringList m_artistNames;
    QStringList m_artistIds;

    // Album data
    QString m_albumName;
    QString m_albumId;
    QUrl m_albumImageSmall;
    QUrl m_albumImageMedium;
    QUrl m_albumImageLarge;
    QString m_releaseDate;

    // Audio features (optional, loaded separately)
    QString m_key;
    QString m_mode;
    float m_tempo;
    float m_energy;
    float m_danceability;
    float m_valence;
    bool m_audioFeaturesLoaded;
};

#endif // SPOTIFYTRACK_H
