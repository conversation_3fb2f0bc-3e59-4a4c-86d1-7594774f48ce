/****************************************************************************
** Meta object code from reading C++ file 'spotifyapi.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../include/spotify/spotifyapi.h"
#include <QtNetwork/QSslError>
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'spotifyapi.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN10SpotifyAPIE_t {};
} // unnamed namespace

template <> constexpr inline auto SpotifyAPI::qt_create_metaobjectdata<qt_meta_tag_ZN10SpotifyAPIE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "SpotifyAPI",
        "searchTracksFinished",
        "",
        "QList<SpotifyTrack*>",
        "tracks",
        "total",
        "offset",
        "searchArtistsFinished",
        "artists",
        "searchAlbumsFinished",
        "albums",
        "searchPlaylistsFinished",
        "playlists",
        "searchAllFinished",
        "results",
        "trackReceived",
        "SpotifyTrack*",
        "track",
        "tracksReceived",
        "audioFeaturesReceived",
        "trackId",
        "features",
        "userSavedTracksReceived",
        "userPlaylistsReceived",
        "playlistTracksReceived",
        "playlistId",
        "currentUserReceived",
        "user",
        "userProfileReceived",
        "recommendationsReceived",
        "apiError",
        "endpoint",
        "statusCode",
        "message",
        "networkError",
        "error",
        "onReplyFinished"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'searchTracksFinished'
        QtMocHelpers::SignalData<void(const QList<SpotifyTrack*> &, int, int)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 }, { QMetaType::Int, 5 }, { QMetaType::Int, 6 },
        }}),
        // Signal 'searchArtistsFinished'
        QtMocHelpers::SignalData<void(const QJsonArray &, int, int)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonArray, 8 }, { QMetaType::Int, 5 }, { QMetaType::Int, 6 },
        }}),
        // Signal 'searchAlbumsFinished'
        QtMocHelpers::SignalData<void(const QJsonArray &, int, int)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonArray, 10 }, { QMetaType::Int, 5 }, { QMetaType::Int, 6 },
        }}),
        // Signal 'searchPlaylistsFinished'
        QtMocHelpers::SignalData<void(const QJsonArray &, int, int)>(11, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonArray, 12 }, { QMetaType::Int, 5 }, { QMetaType::Int, 6 },
        }}),
        // Signal 'searchAllFinished'
        QtMocHelpers::SignalData<void(const QJsonObject &)>(13, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonObject, 14 },
        }}),
        // Signal 'trackReceived'
        QtMocHelpers::SignalData<void(SpotifyTrack *)>(15, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 16, 17 },
        }}),
        // Signal 'tracksReceived'
        QtMocHelpers::SignalData<void(const QList<SpotifyTrack*> &)>(18, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 },
        }}),
        // Signal 'audioFeaturesReceived'
        QtMocHelpers::SignalData<void(const QString &, const QJsonObject &)>(19, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 20 }, { QMetaType::QJsonObject, 21 },
        }}),
        // Signal 'audioFeaturesReceived'
        QtMocHelpers::SignalData<void(const QJsonArray &)>(19, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonArray, 21 },
        }}),
        // Signal 'userSavedTracksReceived'
        QtMocHelpers::SignalData<void(const QList<SpotifyTrack*> &, int, int)>(22, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 }, { QMetaType::Int, 5 }, { QMetaType::Int, 6 },
        }}),
        // Signal 'userPlaylistsReceived'
        QtMocHelpers::SignalData<void(const QJsonArray &, int, int)>(23, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonArray, 12 }, { QMetaType::Int, 5 }, { QMetaType::Int, 6 },
        }}),
        // Signal 'playlistTracksReceived'
        QtMocHelpers::SignalData<void(const QString &, const QList<SpotifyTrack*> &, int, int)>(24, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 25 }, { 0x80000000 | 3, 4 }, { QMetaType::Int, 5 }, { QMetaType::Int, 6 },
        }}),
        // Signal 'currentUserReceived'
        QtMocHelpers::SignalData<void(const QJsonObject &)>(26, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonObject, 27 },
        }}),
        // Signal 'userProfileReceived'
        QtMocHelpers::SignalData<void(const QJsonObject &)>(28, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonObject, 27 },
        }}),
        // Signal 'recommendationsReceived'
        QtMocHelpers::SignalData<void(const QList<SpotifyTrack*> &)>(29, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 },
        }}),
        // Signal 'apiError'
        QtMocHelpers::SignalData<void(const QString &, int, const QString &)>(30, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 31 }, { QMetaType::Int, 32 }, { QMetaType::QString, 33 },
        }}),
        // Signal 'networkError'
        QtMocHelpers::SignalData<void(const QString &, const QString &)>(34, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 31 }, { QMetaType::QString, 35 },
        }}),
        // Slot 'onReplyFinished'
        QtMocHelpers::SlotData<void()>(36, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<SpotifyAPI, qt_meta_tag_ZN10SpotifyAPIE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject SpotifyAPI::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10SpotifyAPIE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10SpotifyAPIE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN10SpotifyAPIE_t>.metaTypes,
    nullptr
} };

void SpotifyAPI::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<SpotifyAPI *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->searchTracksFinished((*reinterpret_cast< std::add_pointer_t<QList<SpotifyTrack*>>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 1: _t->searchArtistsFinished((*reinterpret_cast< std::add_pointer_t<QJsonArray>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 2: _t->searchAlbumsFinished((*reinterpret_cast< std::add_pointer_t<QJsonArray>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 3: _t->searchPlaylistsFinished((*reinterpret_cast< std::add_pointer_t<QJsonArray>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 4: _t->searchAllFinished((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 5: _t->trackReceived((*reinterpret_cast< std::add_pointer_t<SpotifyTrack*>>(_a[1]))); break;
        case 6: _t->tracksReceived((*reinterpret_cast< std::add_pointer_t<QList<SpotifyTrack*>>>(_a[1]))); break;
        case 7: _t->audioFeaturesReceived((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[2]))); break;
        case 8: _t->audioFeaturesReceived((*reinterpret_cast< std::add_pointer_t<QJsonArray>>(_a[1]))); break;
        case 9: _t->userSavedTracksReceived((*reinterpret_cast< std::add_pointer_t<QList<SpotifyTrack*>>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 10: _t->userPlaylistsReceived((*reinterpret_cast< std::add_pointer_t<QJsonArray>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 11: _t->playlistTracksReceived((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QList<SpotifyTrack*>>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[4]))); break;
        case 12: _t->currentUserReceived((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 13: _t->userProfileReceived((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 14: _t->recommendationsReceived((*reinterpret_cast< std::add_pointer_t<QList<SpotifyTrack*>>>(_a[1]))); break;
        case 15: _t->apiError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 16: _t->networkError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 17: _t->onReplyFinished(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QList<SpotifyTrack*> & , int , int )>(_a, &SpotifyAPI::searchTracksFinished, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QJsonArray & , int , int )>(_a, &SpotifyAPI::searchArtistsFinished, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QJsonArray & , int , int )>(_a, &SpotifyAPI::searchAlbumsFinished, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QJsonArray & , int , int )>(_a, &SpotifyAPI::searchPlaylistsFinished, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QJsonObject & )>(_a, &SpotifyAPI::searchAllFinished, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(SpotifyTrack * )>(_a, &SpotifyAPI::trackReceived, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QList<SpotifyTrack*> & )>(_a, &SpotifyAPI::tracksReceived, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QString & , const QJsonObject & )>(_a, &SpotifyAPI::audioFeaturesReceived, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QJsonArray & )>(_a, &SpotifyAPI::audioFeaturesReceived, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QList<SpotifyTrack*> & , int , int )>(_a, &SpotifyAPI::userSavedTracksReceived, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QJsonArray & , int , int )>(_a, &SpotifyAPI::userPlaylistsReceived, 10))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QString & , const QList<SpotifyTrack*> & , int , int )>(_a, &SpotifyAPI::playlistTracksReceived, 11))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QJsonObject & )>(_a, &SpotifyAPI::currentUserReceived, 12))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QJsonObject & )>(_a, &SpotifyAPI::userProfileReceived, 13))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QList<SpotifyTrack*> & )>(_a, &SpotifyAPI::recommendationsReceived, 14))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QString & , int , const QString & )>(_a, &SpotifyAPI::apiError, 15))
            return;
        if (QtMocHelpers::indexOfMethod<void (SpotifyAPI::*)(const QString & , const QString & )>(_a, &SpotifyAPI::networkError, 16))
            return;
    }
}

const QMetaObject *SpotifyAPI::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SpotifyAPI::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10SpotifyAPIE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int SpotifyAPI::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 18)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 18;
    }
    return _id;
}

// SIGNAL 0
void SpotifyAPI::searchTracksFinished(const QList<SpotifyTrack*> & _t1, int _t2, int _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2, _t3);
}

// SIGNAL 1
void SpotifyAPI::searchArtistsFinished(const QJsonArray & _t1, int _t2, int _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2, _t3);
}

// SIGNAL 2
void SpotifyAPI::searchAlbumsFinished(const QJsonArray & _t1, int _t2, int _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1, _t2, _t3);
}

// SIGNAL 3
void SpotifyAPI::searchPlaylistsFinished(const QJsonArray & _t1, int _t2, int _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2, _t3);
}

// SIGNAL 4
void SpotifyAPI::searchAllFinished(const QJsonObject & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void SpotifyAPI::trackReceived(SpotifyTrack * _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}

// SIGNAL 6
void SpotifyAPI::tracksReceived(const QList<SpotifyTrack*> & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1);
}

// SIGNAL 7
void SpotifyAPI::audioFeaturesReceived(const QString & _t1, const QJsonObject & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1, _t2);
}

// SIGNAL 8
void SpotifyAPI::audioFeaturesReceived(const QJsonArray & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 8, nullptr, _t1);
}

// SIGNAL 9
void SpotifyAPI::userSavedTracksReceived(const QList<SpotifyTrack*> & _t1, int _t2, int _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 9, nullptr, _t1, _t2, _t3);
}

// SIGNAL 10
void SpotifyAPI::userPlaylistsReceived(const QJsonArray & _t1, int _t2, int _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 10, nullptr, _t1, _t2, _t3);
}

// SIGNAL 11
void SpotifyAPI::playlistTracksReceived(const QString & _t1, const QList<SpotifyTrack*> & _t2, int _t3, int _t4)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 11, nullptr, _t1, _t2, _t3, _t4);
}

// SIGNAL 12
void SpotifyAPI::currentUserReceived(const QJsonObject & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 12, nullptr, _t1);
}

// SIGNAL 13
void SpotifyAPI::userProfileReceived(const QJsonObject & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 13, nullptr, _t1);
}

// SIGNAL 14
void SpotifyAPI::recommendationsReceived(const QList<SpotifyTrack*> & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 14, nullptr, _t1);
}

// SIGNAL 15
void SpotifyAPI::apiError(const QString & _t1, int _t2, const QString & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 15, nullptr, _t1, _t2, _t3);
}

// SIGNAL 16
void SpotifyAPI::networkError(const QString & _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 16, nullptr, _t1, _t2);
}
QT_WARNING_POP
