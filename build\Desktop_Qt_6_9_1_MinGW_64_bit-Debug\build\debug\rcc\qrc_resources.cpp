/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.9.1
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // dark_theme.qss
  0x0,0x0,0x4,0xbe,
  0x0,
  0x0,0x15,0x61,0x78,0xda,0xb5,0x57,0x5b,0x4f,0xe3,0x38,0x14,0x7e,0xef,0xaf,0xb0,
  0x40,0x2b,0x4d,0x51,0xb3,0x24,0xa1,0x9,0x10,0x9e,0xca,0x30,0x48,0x2b,0x2d,0x12,
  0x23,0xba,0x9a,0x67,0x37,0x31,0xad,0x85,0x1b,0x67,0x13,0x67,0x28,0xb3,0x9a,0xff,
  0xbe,0xbe,0xc4,0xb9,0x3a,0x97,0x32,0x8c,0x23,0x55,0xb1,0x4f,0x7a,0x7c,0x2e,0xdf,
  0x39,0xfe,0x7c,0x7e,0x6,0xee,0x60,0xfa,0x2,0xd6,0x3b,0xb4,0x47,0xe0,0x89,0xbd,
  0x11,0x94,0xed,0x10,0x62,0xe0,0x99,0xa6,0x60,0x95,0x47,0x98,0x82,0x47,0x2,0xdf,
  0x50,0xa,0xce,0xce,0x67,0xb3,0xf3,0x33,0xf0,0x0,0x71,0xc,0xbe,0xe1,0x38,0xa2,
  0xaf,0x62,0xe9,0xab,0x98,0x17,0xd3,0xff,0x66,0x80,0x8f,0xd,0xc,0x5f,0xb6,0x29,
  0xcd,0xe3,0xc8,0xa,0x29,0xa1,0x69,0x0,0x4e,0xdd,0x8d,0x78,0x6e,0xa4,0x58,0xaf,
  0x3d,0xcb,0x71,0x33,0xfb,0x39,0x9b,0x7d,0xfd,0x86,0xa3,0x2d,0xdf,0xf2,0x5d,0xff,
  0x17,0x6b,0xcf,0x34,0x66,0xd6,0x33,0xdc,0x63,0xf2,0x16,0x80,0x93,0x27,0xb4,0xa5,
  0x8,0xfc,0xf3,0xd7,0xc9,0x2,0xac,0x52,0xc,0xc9,0x2,0x64,0x30,0xce,0xac,0xc,
  0xa5,0x58,0xed,0xc7,0xbd,0xb8,0xcd,0x19,0xa3,0x71,0x26,0x3d,0x78,0xcc,0xb3,0x9d,
  0x9a,0xf7,0x5b,0xb0,0xb4,0xc5,0xa3,0x76,0xdb,0xd0,0x34,0x42,0x7c,0xd1,0x49,0xe,
  0x20,0xa3,0x4,0x47,0xe0,0xd4,0x93,0xa3,0x2e,0xb6,0x52,0x18,0xe1,0x3c,0xb,0x80,
  0x9f,0x1c,0xd4,0x7a,0x2,0xa3,0x8,0xc7,0xdb,0x0,0x5c,0xf1,0xff,0x39,0xe5,0xf2,
  0x1e,0xc7,0xd6,0x2b,0x8e,0xd8,0x8e,0xb,0x6c,0xbd,0x28,0x1d,0x7a,0x45,0x78,0xbb,
  0x63,0x1,0xf0,0x6c,0x5b,0x85,0xa9,0x32,0x34,0xd8,0xd1,0xef,0x3c,0x25,0xbd,0xe6,
  0x7a,0xb6,0x78,0x1a,0xf6,0x68,0xd1,0xa5,0x1c,0x1d,0x85,0x49,0x8a,0xb2,0xc,0x45,
  0xfd,0x2a,0x2f,0x3c,0xf1,0x18,0x55,0x5e,0xc8,0xd1,0x51,0x19,0xe1,0xc,0x6e,0xc8,
  0x90,0x4e,0x17,0x8a,0xc7,0xac,0x13,0x8a,0xa7,0x99,0x72,0x5f,0xe,0x9d,0xc2,0xcf,
  0x3c,0x46,0x29,0x25,0x65,0x2a,0x3f,0x9,0x9c,0x2e,0xc0,0x23,0xcc,0x33,0xb4,0x0,
  0x88,0x85,0x7f,0xce,0x5b,0xe9,0x3d,0x4d,0xf8,0x17,0x52,0xae,0xe6,0x8b,0x86,0x30,
  0x63,0x34,0x31,0xad,0xc7,0xe8,0xc0,0x4c,0xeb,0x3c,0x62,0xdf,0x31,0xcd,0x33,0x93,
  0x6c,0x9f,0x33,0xf4,0x1e,0x48,0xb9,0x93,0x20,0xe5,0xd9,0x7f,0x74,0x21,0xd5,0xe,
  0x7f,0xdb,0x59,0x5,0x99,0x3e,0x97,0x4d,0xd2,0xca,0x71,0x93,0xb4,0xe9,0xbe,0xe9,
  0x8b,0x2a,0x8,0x63,0x70,0x75,0x9d,0x6b,0xff,0xfe,0xc2,0x88,0x3,0xe7,0xfa,0xd2,
  0xbf,0x73,0x47,0x9d,0x2b,0xe0,0xdb,0xeb,0x9e,0x51,0x5e,0x73,0xd0,0x28,0x6f,0xb9,
  0x68,0xfc,0xa6,0xe6,0xe4,0x68,0x9,0x39,0x9e,0xef,0x7d,0x36,0x57,0xa5,0x7d,0xb7,
  0xbc,0x5c,0x39,0x1a,0xda,0x4f,0x1c,0x1,0x28,0x55,0xdd,0x49,0xbd,0x7,0x1,0xd7,
  0xc5,0x83,0xc8,0x23,0x99,0xe2,0x1f,0x1c,0xf9,0x90,0xe8,0x7d,0xba,0xdd,0xe8,0x5a,
  0xe,0xb5,0xcf,0xae,0x68,0x22,0x57,0xba,0xb5,0x54,0x76,0x5,0xe0,0x5f,0x82,0x63,
  0x4,0xd3,0xad,0x0,0x16,0x8a,0xd9,0xa7,0x83,0x13,0xd8,0xb,0xf0,0x26,0x7f,0xf,
  0xae,0x7c,0x77,0x3,0x87,0xf7,0x4f,0x1e,0xc8,0xc0,0xd6,0x90,0x2d,0xe6,0x8e,0xee,
  0x9,0xf3,0xa2,0x91,0x71,0x45,0x38,0x56,0x18,0xb6,0x8d,0xb8,0x5d,0x6a,0x94,0x6a,
  0x9f,0x76,0x30,0x8e,0x88,0xc9,0xa7,0x5f,0xb1,0xd1,0x5d,0x79,0xf7,0x5e,0x65,0xa3,
  0xc2,0xd6,0xbc,0xb7,0x75,0x6b,0x80,0x9,0x71,0xd1,0x87,0x9d,0x32,0x58,0xda,0x27,
  0xab,0xdf,0xa9,0xeb,0x51,0xa7,0x7a,0xd0,0x7f,0xbc,0x6b,0xfe,0xf2,0xd6,0xbb,0xf7,
  0x2b,0xd7,0x94,0xab,0x73,0x73,0xdd,0x7c,0xb9,0xba,0xfa,0xe2,0x8d,0x59,0xd6,0x7,
  0xd9,0xe3,0x6d,0x53,0x61,0xac,0x6c,0x53,0x58,0x9f,0x8f,0x81,0xbd,0xb4,0x2d,0xcb,
  0x37,0x56,0x2,0xb7,0x1f,0xc,0x6,0x95,0xfc,0x9a,0x55,0xd2,0xca,0x1,0x30,0xd4,
  0x2a,0xb4,0x5b,0x39,0x66,0x34,0xf3,0x7a,0xfd,0x1b,0x67,0xc,0x14,0x24,0x46,0x1e,
  0x43,0x84,0x2f,0xa8,0xd3,0x47,0x88,0xc6,0xe8,0x4d,0xf7,0x68,0x3d,0x9a,0x5c,0x40,
  0xc2,0x50,0x1a,0x43,0x86,0xac,0x91,0x93,0x26,0x43,0x4,0x85,0xc,0xd3,0xd8,0x1a,
  0xe9,0xc3,0x34,0x67,0x22,0xd8,0x1,0x88,0x69,0x8c,0x54,0xae,0x2a,0x5f,0x82,0x0,
  0x33,0xb4,0x2f,0x3c,0x2a,0x4f,0x21,0xc7,0x6e,0x85,0x6a,0x43,0x79,0x63,0xdc,0x37,
  0x9c,0x59,0xca,0xd1,0x4f,0x5,0xdb,0x7b,0x4,0xca,0xe0,0x41,0x16,0x51,0xb3,0x7a,
  0x92,0xca,0x89,0xe4,0x69,0xd0,0x9c,0x31,0x25,0xb5,0xfa,0x13,0x0,0x81,0x1b,0x44,
  0x54,0x3f,0x97,0xaf,0xbd,0xff,0x63,0x29,0xa7,0xaa,0x9,0x4c,0x39,0xb8,0x7,0x1c,
  0x12,0x2a,0x4e,0xf9,0xa7,0xe1,0xcb,0x1a,0x33,0x82,0xea,0x2a,0x25,0x73,0xcc,0xf0,
  0xf,0x54,0x6f,0x63,0xd,0x3a,0xb9,0xa1,0x24,0x9a,0xa4,0x7b,0x95,0x32,0xee,0x7d,
  0x9f,0xf2,0xa5,0x56,0xae,0xb5,0x84,0x72,0xd4,0xb5,0x24,0x45,0x25,0x8c,0xaa,0x98,
  0x68,0x5f,0x3,0x6c,0x9e,0xea,0xc8,0xd5,0x6e,0x61,0x9e,0x8a,0xb0,0xad,0xf1,0x5e,
  0x5,0x64,0x51,0x3a,0x43,0x79,0x33,0x29,0x97,0xeb,0x76,0x94,0x57,0x6,0xce,0x25,
  0x39,0x3e,0x61,0xc6,0xaf,0xc,0x27,0xf,0x34,0x86,0x21,0xe5,0x6f,0x7b,0x1a,0x53,
  0x9e,0x8b,0x10,0xdd,0x74,0x2c,0x77,0xfb,0x9c,0x6f,0x31,0xfa,0xa5,0x57,0x35,0x89,
  0x7,0x14,0xe7,0xe0,0x16,0xa6,0xea,0xd6,0xc4,0x27,0xe2,0xfd,0x18,0xba,0x6d,0xa8,
  0xa6,0x7a,0x6b,0x18,0x8c,0x96,0xab,0x4f,0xa9,0x62,0xe3,0x46,0x1,0x4f,0x80,0x61,
  0xa9,0xc8,0x17,0x97,0x17,0x77,0xa4,0x25,0x36,0x77,0xf9,0x80,0x12,0x6e,0xea,0x1b,
  0x27,0x5a,0x15,0x69,0xd4,0x81,0xd7,0x41,0xff,0xad,0x5d,0xb8,0x8c,0x52,0x23,0x10,
  0xe6,0x66,0x29,0x6e,0x81,0xee,0x72,0x4a,0x20,0x3f,0x34,0x8a,0xfc,0xa4,0x45,0x3c,
  0xb1,0x90,0x51,0xd,0x3e,0x7d,0xce,0x39,0x5d,0x86,0x58,0xb5,0xc4,0x9a,0xf3,0x9a,
  0x13,0x71,0x1b,0xcb,0x7b,0x87,0xe0,0xac,0xc,0xb2,0x3c,0x2b,0x1,0xae,0xa6,0xc7,
  0x42,0x5c,0x1c,0xd3,0x63,0xf8,0xae,0x97,0x5a,0x27,0xe0,0xc2,0x90,0x84,0x60,0xc6,
  0x50,0x61,0x46,0x31,0xd1,0xe4,0x67,0xa0,0xeb,0x17,0x5b,0x49,0x52,0xd2,0xfa,0x57,
  0x97,0x94,0x14,0xf5,0x7d,0xd1,0x93,0x3f,0xa7,0x24,0x85,0x5d,0x4d,0x83,0xc7,0x46,
  0xed,0x76,0x2e,0x5c,0x9,0xf9,0x5,0x97,0x88,0x98,0x16,0x77,0x1,0x39,0x17,0x85,
  0xc0,0x95,0x30,0x1c,0x42,0x32,0xed,0x7a,0xa9,0x79,0x6d,0x5f,0xdd,0xfa,0xa5,0xb9,
  0xe5,0x6,0xda,0xde,0xf1,0x8d,0xf4,0xad,0x7c,0xa8,0x30,0x44,0x4b,0xd4,0x38,0x73,
  0xed,0xf1,0xdd,0x8e,0x8,0x53,0x5d,0xb,0x47,0x83,0x25,0x49,0x8b,0xd6,0xb3,0x68,
  0x88,0x5,0xc7,0x6c,0x88,0x5b,0x57,0x28,0xc5,0x74,0xda,0x9c,0xb3,0xe2,0x3f,0x95,
  0xaa,0x1,0x92,0x6a,0x4c,0x40,0x59,0x63,0xef,0xc8,0xc0,0x94,0xbd,0xa6,0xe6,0xa0,
  0x0,0xc2,0x50,0xa,0x46,0xef,0x2d,0x93,0x93,0x50,0x69,0xea,0x49,0x43,0xef,0x5d,
  0x76,0x24,0x11,0xbc,0x32,0xd6,0x94,0x12,0x86,0x13,0x55,0x16,0x62,0xb2,0xc6,0xc9,
  0x40,0x2a,0x3c,0xf1,0xf4,0x1f,0x93,0x86,0x76,0x3f,0x10,0xd1,0x65,0xa7,0xdd,0xfb,
  0xd,0x22,0x53,0xe7,0x8,0x3f,0x67,0xff,0x3,0xcd,0xcc,0x9e,0xb,
    // play.svg
  0x0,0x0,0x0,0xf8,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x38,0x20,0x35,0x56,0x31,0x39,0x4c,0x31,0x39,0x20,0x31,0x32,0x4c,0x38,
  0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,
  0x66,0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,
  0x68,0x3d,0x22,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,
  0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // pause.svg
  0x0,0x0,0x1,0x5,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x36,0x22,0x20,0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,
  0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,
  0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,0x22,0x31,0x34,0x22,0x20,
  0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x34,0x22,0x20,
  0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,
  0x76,0x67,0x3e,0xa,
    // previous.svg
  0x0,0x0,0x0,0xfa,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x32,0x30,0x20,0x35,0x56,0x31,0x39,0x4c,0x39,0x20,0x31,0x32,0x4c,0x32,
  0x30,0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,
  0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,
  0x3d,0x22,0x34,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x33,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x34,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // volume.svg
  0x0,0x0,0x1,0xe4,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x39,0x2e,0x30,0x37,0x20,0x34,
  0x2e,0x39,0x33,0x43,0x32,0x30,0x2e,0x39,0x20,0x36,0x2e,0x37,0x36,0x20,0x32,0x31,
  0x2e,0x39,0x20,0x39,0x2e,0x33,0x20,0x32,0x31,0x2e,0x39,0x20,0x31,0x32,0x43,0x32,
  0x31,0x2e,0x39,0x20,0x31,0x34,0x2e,0x37,0x20,0x32,0x30,0x2e,0x39,0x20,0x31,0x37,
  0x2e,0x32,0x34,0x20,0x31,0x39,0x2e,0x30,0x37,0x20,0x31,0x39,0x2e,0x30,0x37,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,
  0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x35,0x2e,0x35,0x34,0x20,0x38,0x2e,
  0x34,0x36,0x43,0x31,0x36,0x2e,0x34,0x20,0x39,0x2e,0x33,0x32,0x20,0x31,0x36,0x2e,
  0x39,0x20,0x31,0x30,0x2e,0x36,0x20,0x31,0x36,0x2e,0x39,0x20,0x31,0x32,0x43,0x31,
  0x36,0x2e,0x39,0x20,0x31,0x33,0x2e,0x34,0x20,0x31,0x36,0x2e,0x34,0x20,0x31,0x34,
  0x2e,0x36,0x38,0x20,0x31,0x35,0x2e,0x35,0x34,0x20,0x31,0x35,0x2e,0x35,0x34,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // next.svg
  0x0,0x0,0x0,0xfa,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x34,0x20,0x35,0x56,0x31,0x39,0x4c,0x31,0x35,0x20,0x31,0x32,0x4c,0x34,
  0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x31,0x37,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x33,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x34,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // add.svg
  0x0,0x0,0x0,0xe6,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x32,0x20,0x35,0x56,0x31,0x39,0x4d,0x35,0x20,0x31,0x32,0x48,0x31,
  0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,
  0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,
  0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,0xa,
    // app_icon.svg
  0x0,0x0,0x0,0xf4,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x34,0x38,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x34,0x38,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x34,
  0x38,0x20,0x34,0x38,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,
  0x63,0x78,0x3d,0x22,0x32,0x34,0x22,0x20,0x63,0x79,0x3d,0x22,0x32,0x34,0x22,0x20,
  0x72,0x3d,0x22,0x32,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x32,0x31,
  0x39,0x36,0x46,0x33,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,
  0x64,0x3d,0x22,0x4d,0x31,0x38,0x20,0x31,0x34,0x56,0x33,0x34,0x4c,0x33,0x34,0x20,
  0x32,0x34,0x4c,0x31,0x38,0x20,0x31,0x34,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,
  0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // volume_mute.svg
  0x0,0x0,0x1,0x23,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x32,0x33,0x20,0x39,0x4c,0x31,0x37,
  0x20,0x31,0x35,0x4d,0x31,0x37,0x20,0x39,0x4c,0x32,0x33,0x20,0x31,0x35,0x22,0x20,
  0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,
  0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,
  0x3e,0xa,
    // remove.svg
  0x0,0x0,0x0,0xde,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x35,0x20,0x31,0x32,0x48,0x31,0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,
  0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // stop.svg
  0x0,0x0,0x0,0xca,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x36,0x22,0x20,0x79,0x3d,0x22,0x36,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x31,0x32,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x32,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x66,0x66,0x66,0x66,0x66,0x66,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // styles
  0x0,0x6,
  0x7,0xac,0x2,0xc3,
  0x0,0x73,
  0x0,0x74,0x0,0x79,0x0,0x6c,0x0,0x65,0x0,0x73,
    // dark_theme.qss
  0x0,0xe,
  0xd,0x16,0x97,0xc3,
  0x0,0x64,
  0x0,0x61,0x0,0x72,0x0,0x6b,0x0,0x5f,0x0,0x74,0x0,0x68,0x0,0x65,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x71,0x0,0x73,0x0,0x73,
    // play.svg
  0x0,0x8,
  0x2,0x8c,0x54,0x27,
  0x0,0x70,
  0x0,0x6c,0x0,0x61,0x0,0x79,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // pause.svg
  0x0,0x9,
  0xc,0x98,0xb7,0xc7,
  0x0,0x70,
  0x0,0x61,0x0,0x75,0x0,0x73,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // previous.svg
  0x0,0xc,
  0x8,0x37,0xc0,0xc7,
  0x0,0x70,
  0x0,0x72,0x0,0x65,0x0,0x76,0x0,0x69,0x0,0x6f,0x0,0x75,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume.svg
  0x0,0xa,
  0xc,0x3b,0xf6,0xa7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // next.svg
  0x0,0x8,
  0xc,0xf7,0x54,0x47,
  0x0,0x6e,
  0x0,0x65,0x0,0x78,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // add.svg
  0x0,0x7,
  0x7,0xa7,0x5a,0x7,
  0x0,0x61,
  0x0,0x64,0x0,0x64,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // app_icon.svg
  0x0,0xc,
  0x7,0x6f,0x91,0x27,
  0x0,0x61,
  0x0,0x70,0x0,0x70,0x0,0x5f,0x0,0x69,0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume_mute.svg
  0x0,0xf,
  0xb,0x22,0xfb,0xc7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x5f,0x0,0x6d,0x0,0x75,0x0,0x74,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // remove.svg
  0x0,0xa,
  0x6,0xcb,0x42,0x47,
  0x0,0x72,
  0x0,0x65,0x0,0x6d,0x0,0x6f,0x0,0x76,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // stop.svg
  0x0,0x8,
  0xb,0x63,0x55,0x87,
  0x0,0x73,
  0x0,0x74,0x0,0x6f,0x0,0x70,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0xa,0x0,0x0,0x0,0x4,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles
  0x0,0x0,0x0,0x10,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles/dark_theme.qss
  0x0,0x0,0x0,0x22,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xd9,0x91,0x94,0x73,
  // :/icons/play.svg
  0x0,0x0,0x0,0x44,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x4,0xc2,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x6a,0xb1,
  // :/icons/remove.svg
  0x0,0x0,0x1,0x16,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xd,0xb4,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x1a,0x8,
  // :/icons/app_icon.svg
  0x0,0x0,0x0,0xd4,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xb,0x95,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x2f,0xeb,
  // :/icons/add.svg
  0x0,0x0,0x0,0xc0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xa,0xab,
0x0,0x0,0x1,0x97,0xd9,0x8c,0x5,0xd4,
  // :/icons/previous.svg
  0x0,0x0,0x0,0x72,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x6,0xc7,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xbb,0x98,
  // :/icons/volume_mute.svg
  0x0,0x0,0x0,0xf2,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xc,0x8d,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xf2,0x4e,
  // :/icons/stop.svg
  0x0,0x0,0x1,0x30,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xe,0x96,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x93,0x2b,
  // :/icons/volume.svg
  0x0,0x0,0x0,0x90,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x7,0xc5,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xdb,0xe5,
  // :/icons/pause.svg
  0x0,0x0,0x0,0x5a,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x5,0xbe,
0x0,0x0,0x1,0x97,0xd9,0x8b,0x7f,0x6f,
  // :/icons/next.svg
  0x0,0x0,0x0,0xaa,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x9,0xad,
0x0,0x0,0x1,0x97,0xd9,0x8b,0xa7,0x9c,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
