#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QLabel>
#include <QSlider>
#include <QPushButton>
#include <QListView>
#include <QFileDialog>
#include <QMessageBox>
#include <QKeyEvent>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QCloseEvent>

QT_BEGIN_NAMESPACE
class QAction;
class QMenu;
QT_END_NAMESPACE

#include "audioplayer.h"
#include "playlist.h"
#include "playlistmodel.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void keyPressEvent(QKeyEvent *event) override;
    void closeEvent(QCloseEvent *event) override;
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;

private slots:
    // Audio player slots
    void onPlaybackStateChanged(AudioPlayer::PlaybackState state);
    void onPositionChanged(qint64 position);
    void onDurationChanged(qint64 duration);
    void onVolumeChanged(float volume);
    void onMutedChanged(bool muted);
    void onErrorOccurred(const QString &error);
    
    // Control slots
    void playPause();
    void stop();
    void next();
    void previous();
    void setPosition(int position);
    void setVolume(int volume);
    void toggleMute();
    
    // Playlist slots
    void addFiles();
    void removeSelectedItems();
    void onPlaylistItemDoubleClicked(const QModelIndex &index);
    void onCurrentPlaylistItemChanged(int index);
    
    // Menu actions
    void openFiles();
    void newPlaylist();
    void openPlaylist();
    void savePlaylist();
    void savePlaylistAs();
    void about();
    
    // Timer slot
    void updatePosition();

private:
    Ui::MainWindow *ui;
    
    // Core components
    AudioPlayer *m_audioPlayer;
    Playlist *m_playlist;
    PlaylistModel *m_playlistModel;
    
    // UI update timer
    QTimer *m_positionTimer;
    
    // Current playlist file
    QString m_currentPlaylistFile;
    bool m_playlistModified;
    
    // Helper methods
    void setupConnections();
    void updatePlayPauseButton();
    void updateTrackInfo();
    void updateAlbumArt(const PlaylistItem &item);
    void updateTimeLabels(qint64 position, qint64 duration);
    QString formatTime(qint64 milliseconds) const;
    void loadSettings();
    void saveSettings();
    bool confirmPlaylistDiscard();
    
    // File operations
    QStringList getSupportedAudioFiles();
    QStringList getAudioFileFilters();
};

#endif // MAINWINDOW_H
