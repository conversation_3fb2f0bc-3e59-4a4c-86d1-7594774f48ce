/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 6.9.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtGui/QAction>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QListView>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenu>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSlider>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QAction *actionOpen_Files;
    QAction *actionExit;
    QAction *actionPlay_Pause;
    QAction *actionStop;
    QAction *actionNext;
    QAction *actionPrevious;
    QAction *actionNew_Playlist;
    QAction *actionOpen_Playlist;
    QAction *actionSave_Playlist;
    QAction *actionSave_Playlist_As;
    QAction *actionAbout;
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout_main;
    QSplitter *splitter;
    QWidget *playlistWidget;
    QVBoxLayout *verticalLayout_playlist;
    QLabel *playlistLabel;
    QHBoxLayout *playlistButtonsLayout;
    QPushButton *addFilesButton;
    QPushButton *removeFileButton;
    QSpacerItem *horizontalSpacer;
    QListView *playlistView;
    QWidget *playerWidget;
    QVBoxLayout *verticalLayout_player;
    QSpacerItem *verticalSpacer;
    QWidget *trackInfoWidget;
    QVBoxLayout *verticalLayout_trackInfo;
    QLabel *trackTitleLabel;
    QLabel *trackArtistLabel;
    QWidget *progressWidget;
    QHBoxLayout *horizontalLayout_progress;
    QLabel *currentTimeLabel;
    QSlider *progressSlider;
    QLabel *totalTimeLabel;
    QWidget *controlsWidget;
    QHBoxLayout *horizontalLayout_controls;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *previousButton;
    QPushButton *playPauseButton;
    QPushButton *stopButton;
    QPushButton *nextButton;
    QSpacerItem *horizontalSpacer_3;
    QWidget *volumeWidget;
    QHBoxLayout *horizontalLayout_volume;
    QSpacerItem *horizontalSpacer_4;
    QPushButton *muteButton;
    QSlider *volumeSlider;
    QSpacerItem *horizontalSpacer_5;
    QSpacerItem *verticalSpacer_2;
    QMenuBar *menubar;
    QMenu *menuFile;
    QMenu *menuPlayback;
    QMenu *menuHelp;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName("MainWindow");
        MainWindow->resize(1000, 700);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/app_icon.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        MainWindow->setWindowIcon(icon);
        actionOpen_Files = new QAction(MainWindow);
        actionOpen_Files->setObjectName("actionOpen_Files");
        actionExit = new QAction(MainWindow);
        actionExit->setObjectName("actionExit");
        actionPlay_Pause = new QAction(MainWindow);
        actionPlay_Pause->setObjectName("actionPlay_Pause");
        actionStop = new QAction(MainWindow);
        actionStop->setObjectName("actionStop");
        actionNext = new QAction(MainWindow);
        actionNext->setObjectName("actionNext");
        actionPrevious = new QAction(MainWindow);
        actionPrevious->setObjectName("actionPrevious");
        actionNew_Playlist = new QAction(MainWindow);
        actionNew_Playlist->setObjectName("actionNew_Playlist");
        actionOpen_Playlist = new QAction(MainWindow);
        actionOpen_Playlist->setObjectName("actionOpen_Playlist");
        actionSave_Playlist = new QAction(MainWindow);
        actionSave_Playlist->setObjectName("actionSave_Playlist");
        actionSave_Playlist_As = new QAction(MainWindow);
        actionSave_Playlist_As->setObjectName("actionSave_Playlist_As");
        actionAbout = new QAction(MainWindow);
        actionAbout->setObjectName("actionAbout");
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName("centralwidget");
        verticalLayout_main = new QVBoxLayout(centralwidget);
        verticalLayout_main->setSpacing(0);
        verticalLayout_main->setObjectName("verticalLayout_main");
        verticalLayout_main->setContentsMargins(0, 0, 0, 0);
        splitter = new QSplitter(centralwidget);
        splitter->setObjectName("splitter");
        splitter->setOrientation(Qt::Horizontal);
        playlistWidget = new QWidget(splitter);
        playlistWidget->setObjectName("playlistWidget");
        playlistWidget->setMinimumSize(QSize(300, 0));
        verticalLayout_playlist = new QVBoxLayout(playlistWidget);
        verticalLayout_playlist->setSpacing(5);
        verticalLayout_playlist->setObjectName("verticalLayout_playlist");
        verticalLayout_playlist->setContentsMargins(10, 10, 5, 10);
        playlistLabel = new QLabel(playlistWidget);
        playlistLabel->setObjectName("playlistLabel");

        verticalLayout_playlist->addWidget(playlistLabel);

        playlistButtonsLayout = new QHBoxLayout();
        playlistButtonsLayout->setObjectName("playlistButtonsLayout");
        addFilesButton = new QPushButton(playlistWidget);
        addFilesButton->setObjectName("addFilesButton");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/add.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        addFilesButton->setIcon(icon1);

        playlistButtonsLayout->addWidget(addFilesButton);

        removeFileButton = new QPushButton(playlistWidget);
        removeFileButton->setObjectName("removeFileButton");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/remove.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        removeFileButton->setIcon(icon2);

        playlistButtonsLayout->addWidget(removeFileButton);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        playlistButtonsLayout->addItem(horizontalSpacer);


        verticalLayout_playlist->addLayout(playlistButtonsLayout);

        playlistView = new QListView(playlistWidget);
        playlistView->setObjectName("playlistView");
        playlistView->setDragDropMode(QAbstractItemView::InternalMove);
        playlistView->setAlternatingRowColors(true);

        verticalLayout_playlist->addWidget(playlistView);

        splitter->addWidget(playlistWidget);
        playerWidget = new QWidget(splitter);
        playerWidget->setObjectName("playerWidget");
        verticalLayout_player = new QVBoxLayout(playerWidget);
        verticalLayout_player->setSpacing(10);
        verticalLayout_player->setObjectName("verticalLayout_player");
        verticalLayout_player->setContentsMargins(5, 10, 10, 10);
        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout_player->addItem(verticalSpacer);

        trackInfoWidget = new QWidget(playerWidget);
        trackInfoWidget->setObjectName("trackInfoWidget");
        verticalLayout_trackInfo = new QVBoxLayout(trackInfoWidget);
        verticalLayout_trackInfo->setSpacing(5);
        verticalLayout_trackInfo->setObjectName("verticalLayout_trackInfo");
        trackTitleLabel = new QLabel(trackInfoWidget);
        trackTitleLabel->setObjectName("trackTitleLabel");
        trackTitleLabel->setAlignment(Qt::AlignCenter);

        verticalLayout_trackInfo->addWidget(trackTitleLabel);

        trackArtistLabel = new QLabel(trackInfoWidget);
        trackArtistLabel->setObjectName("trackArtistLabel");
        trackArtistLabel->setAlignment(Qt::AlignCenter);

        verticalLayout_trackInfo->addWidget(trackArtistLabel);


        verticalLayout_player->addWidget(trackInfoWidget);

        progressWidget = new QWidget(playerWidget);
        progressWidget->setObjectName("progressWidget");
        horizontalLayout_progress = new QHBoxLayout(progressWidget);
        horizontalLayout_progress->setSpacing(10);
        horizontalLayout_progress->setObjectName("horizontalLayout_progress");
        currentTimeLabel = new QLabel(progressWidget);
        currentTimeLabel->setObjectName("currentTimeLabel");
        currentTimeLabel->setMinimumSize(QSize(40, 0));

        horizontalLayout_progress->addWidget(currentTimeLabel);

        progressSlider = new QSlider(progressWidget);
        progressSlider->setObjectName("progressSlider");
        progressSlider->setOrientation(Qt::Horizontal);

        horizontalLayout_progress->addWidget(progressSlider);

        totalTimeLabel = new QLabel(progressWidget);
        totalTimeLabel->setObjectName("totalTimeLabel");
        totalTimeLabel->setMinimumSize(QSize(40, 0));

        horizontalLayout_progress->addWidget(totalTimeLabel);


        verticalLayout_player->addWidget(progressWidget);

        controlsWidget = new QWidget(playerWidget);
        controlsWidget->setObjectName("controlsWidget");
        horizontalLayout_controls = new QHBoxLayout(controlsWidget);
        horizontalLayout_controls->setSpacing(10);
        horizontalLayout_controls->setObjectName("horizontalLayout_controls");
        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_controls->addItem(horizontalSpacer_2);

        previousButton = new QPushButton(controlsWidget);
        previousButton->setObjectName("previousButton");
        previousButton->setMinimumSize(QSize(50, 50));
        previousButton->setMaximumSize(QSize(50, 50));
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/icons/previous.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        previousButton->setIcon(icon3);
        previousButton->setIconSize(QSize(32, 32));

        horizontalLayout_controls->addWidget(previousButton);

        playPauseButton = new QPushButton(controlsWidget);
        playPauseButton->setObjectName("playPauseButton");
        playPauseButton->setMinimumSize(QSize(60, 60));
        playPauseButton->setMaximumSize(QSize(60, 60));
        QIcon icon4;
        icon4.addFile(QString::fromUtf8(":/icons/play.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        playPauseButton->setIcon(icon4);
        playPauseButton->setIconSize(QSize(40, 40));

        horizontalLayout_controls->addWidget(playPauseButton);

        stopButton = new QPushButton(controlsWidget);
        stopButton->setObjectName("stopButton");
        stopButton->setMinimumSize(QSize(50, 50));
        stopButton->setMaximumSize(QSize(50, 50));
        QIcon icon5;
        icon5.addFile(QString::fromUtf8(":/icons/stop.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        stopButton->setIcon(icon5);
        stopButton->setIconSize(QSize(32, 32));

        horizontalLayout_controls->addWidget(stopButton);

        nextButton = new QPushButton(controlsWidget);
        nextButton->setObjectName("nextButton");
        nextButton->setMinimumSize(QSize(50, 50));
        nextButton->setMaximumSize(QSize(50, 50));
        QIcon icon6;
        icon6.addFile(QString::fromUtf8(":/icons/next.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        nextButton->setIcon(icon6);
        nextButton->setIconSize(QSize(32, 32));

        horizontalLayout_controls->addWidget(nextButton);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_controls->addItem(horizontalSpacer_3);


        verticalLayout_player->addWidget(controlsWidget);

        volumeWidget = new QWidget(playerWidget);
        volumeWidget->setObjectName("volumeWidget");
        horizontalLayout_volume = new QHBoxLayout(volumeWidget);
        horizontalLayout_volume->setSpacing(10);
        horizontalLayout_volume->setObjectName("horizontalLayout_volume");
        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_volume->addItem(horizontalSpacer_4);

        muteButton = new QPushButton(volumeWidget);
        muteButton->setObjectName("muteButton");
        muteButton->setMinimumSize(QSize(30, 30));
        muteButton->setMaximumSize(QSize(30, 30));
        QIcon icon7;
        icon7.addFile(QString::fromUtf8(":/icons/volume.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        muteButton->setIcon(icon7);
        muteButton->setIconSize(QSize(20, 20));

        horizontalLayout_volume->addWidget(muteButton);

        volumeSlider = new QSlider(volumeWidget);
        volumeSlider->setObjectName("volumeSlider");
        volumeSlider->setMaximum(100);
        volumeSlider->setValue(70);
        volumeSlider->setOrientation(Qt::Horizontal);

        horizontalLayout_volume->addWidget(volumeSlider);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_volume->addItem(horizontalSpacer_5);


        verticalLayout_player->addWidget(volumeWidget);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout_player->addItem(verticalSpacer_2);

        splitter->addWidget(playerWidget);

        verticalLayout_main->addWidget(splitter);

        MainWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName("menubar");
        menubar->setGeometry(QRect(0, 0, 1000, 22));
        menuFile = new QMenu(menubar);
        menuFile->setObjectName("menuFile");
        menuPlayback = new QMenu(menubar);
        menuPlayback->setObjectName("menuPlayback");
        menuHelp = new QMenu(menubar);
        menuHelp->setObjectName("menuHelp");
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName("statusbar");
        MainWindow->setStatusBar(statusbar);

        menubar->addAction(menuFile->menuAction());
        menubar->addAction(menuPlayback->menuAction());
        menubar->addAction(menuHelp->menuAction());
        menuFile->addAction(actionOpen_Files);
        menuFile->addSeparator();
        menuFile->addAction(actionNew_Playlist);
        menuFile->addAction(actionOpen_Playlist);
        menuFile->addAction(actionSave_Playlist);
        menuFile->addAction(actionSave_Playlist_As);
        menuFile->addSeparator();
        menuFile->addAction(actionExit);
        menuPlayback->addAction(actionPlay_Pause);
        menuPlayback->addAction(actionStop);
        menuPlayback->addAction(actionNext);
        menuPlayback->addAction(actionPrevious);
        menuHelp->addAction(actionAbout);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "Audio Player", nullptr));
        actionOpen_Files->setText(QCoreApplication::translate("MainWindow", "Open Files...", nullptr));
#if QT_CONFIG(shortcut)
        actionOpen_Files->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+O", nullptr));
#endif // QT_CONFIG(shortcut)
        actionExit->setText(QCoreApplication::translate("MainWindow", "Exit", nullptr));
#if QT_CONFIG(shortcut)
        actionExit->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+Q", nullptr));
#endif // QT_CONFIG(shortcut)
        actionPlay_Pause->setText(QCoreApplication::translate("MainWindow", "Play/Pause", nullptr));
#if QT_CONFIG(shortcut)
        actionPlay_Pause->setShortcut(QCoreApplication::translate("MainWindow", "Space", nullptr));
#endif // QT_CONFIG(shortcut)
        actionStop->setText(QCoreApplication::translate("MainWindow", "Stop", nullptr));
#if QT_CONFIG(shortcut)
        actionStop->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+.", nullptr));
#endif // QT_CONFIG(shortcut)
        actionNext->setText(QCoreApplication::translate("MainWindow", "Next", nullptr));
#if QT_CONFIG(shortcut)
        actionNext->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+Right", nullptr));
#endif // QT_CONFIG(shortcut)
        actionPrevious->setText(QCoreApplication::translate("MainWindow", "Previous", nullptr));
#if QT_CONFIG(shortcut)
        actionPrevious->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+Left", nullptr));
#endif // QT_CONFIG(shortcut)
        actionNew_Playlist->setText(QCoreApplication::translate("MainWindow", "New Playlist", nullptr));
#if QT_CONFIG(shortcut)
        actionNew_Playlist->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+N", nullptr));
#endif // QT_CONFIG(shortcut)
        actionOpen_Playlist->setText(QCoreApplication::translate("MainWindow", "Open Playlist...", nullptr));
#if QT_CONFIG(shortcut)
        actionOpen_Playlist->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+Shift+O", nullptr));
#endif // QT_CONFIG(shortcut)
        actionSave_Playlist->setText(QCoreApplication::translate("MainWindow", "Save Playlist", nullptr));
#if QT_CONFIG(shortcut)
        actionSave_Playlist->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+S", nullptr));
#endif // QT_CONFIG(shortcut)
        actionSave_Playlist_As->setText(QCoreApplication::translate("MainWindow", "Save Playlist As...", nullptr));
#if QT_CONFIG(shortcut)
        actionSave_Playlist_As->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+Shift+S", nullptr));
#endif // QT_CONFIG(shortcut)
        actionAbout->setText(QCoreApplication::translate("MainWindow", "About", nullptr));
        playlistLabel->setText(QCoreApplication::translate("MainWindow", "Playlist", nullptr));
        playlistLabel->setStyleSheet(QCoreApplication::translate("MainWindow", "font-weight: bold; font-size: 14px;", nullptr));
        addFilesButton->setText(QCoreApplication::translate("MainWindow", "Add Files", nullptr));
        removeFileButton->setText(QCoreApplication::translate("MainWindow", "Remove", nullptr));
        trackTitleLabel->setText(QCoreApplication::translate("MainWindow", "No track selected", nullptr));
        trackTitleLabel->setStyleSheet(QCoreApplication::translate("MainWindow", "font-size: 18px; font-weight: bold;", nullptr));
        trackArtistLabel->setText(QString());
        trackArtistLabel->setStyleSheet(QCoreApplication::translate("MainWindow", "font-size: 14px;", nullptr));
        currentTimeLabel->setText(QCoreApplication::translate("MainWindow", "00:00", nullptr));
        totalTimeLabel->setText(QCoreApplication::translate("MainWindow", "00:00", nullptr));
        previousButton->setText(QString());
        playPauseButton->setText(QString());
        stopButton->setText(QString());
        nextButton->setText(QString());
        muteButton->setText(QString());
        menuFile->setTitle(QCoreApplication::translate("MainWindow", "File", nullptr));
        menuPlayback->setTitle(QCoreApplication::translate("MainWindow", "Playback", nullptr));
        menuHelp->setTitle(QCoreApplication::translate("MainWindow", "Help", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
