@echo off
echo ========================================
echo Audio Player Installer Builder
echo ========================================
echo.

REM Set paths
set QT_DIR=C:\Qt\6.9.1\mingw_64
set MINGW_DIR=C:\Qt\Tools\mingw1310_64
set NSIS_DIR=C:\Program Files (x86)\NSIS
set PROJECT_DIR=%~dp0

REM Add Qt and MinGW to PATH
set PATH=%PATH%;%QT_DIR%\bin;%MINGW_DIR%\bin

echo [1/6] Checking prerequisites...

REM Check if NSIS is installed
if not exist "%NSIS_DIR%\makensis.exe" (
    echo ERROR: NSIS not found at "%NSIS_DIR%"
    echo Please install NSIS from https://nsis.sourceforge.io/
    echo Or update the NSIS_DIR path in this script
    pause
    exit /b 1
)

REM Check if Qt is installed
if not exist "%QT_DIR%\bin\qmake.exe" (
    echo ERROR: Qt not found at "%QT_DIR%"
    echo Please install Qt 6.9.1 or update the QT_DIR path in this script
    pause
    exit /b 1
)

REM Check if MinGW is installed
if not exist "%MINGW_DIR%\bin\gcc.exe" (
    echo ERROR: MinGW not found at "%MINGW_DIR%"
    echo Please install Qt with MinGW or update the MINGW_DIR path in this script
    pause
    exit /b 1
)

echo Prerequisites check passed!
echo.

echo [2/6] Cleaning previous build...
if exist "build" rmdir /s /q "build"
if exist "debug" rmdir /s /q "debug"
if exist "release" rmdir /s /q "release"
if exist "Makefile*" del /q "Makefile*"
if exist "*.o" del /q "*.o"
if exist "AudioPlayer_Setup.exe" del /q "AudioPlayer_Setup.exe"

echo [3/6] Building application...
echo Running qmake...
qmake AudioPlayer.pro
if errorlevel 1 (
    echo ERROR: qmake failed
    pause
    exit /b 1
)

echo Running make...
mingw32-make release
if errorlevel 1 (
    echo ERROR: make failed
    pause
    exit /b 1
)

echo [4/6] Checking if executable was built...
if not exist "build\release\AudioPlayer.exe" (
    echo ERROR: AudioPlayer.exe not found in build\release\
    echo Build may have failed
    pause
    exit /b 1
)

echo [5/6] Deploying Qt dependencies...
echo Running windeployqt...
"%QT_DIR%\bin\windeployqt.exe" --release --no-translations --no-system-d3d-compiler --no-opengl-sw "build\release\AudioPlayer.exe"
if errorlevel 1 (
    echo WARNING: windeployqt had issues, but continuing...
)

echo [6/6] Building installer with NSIS...
echo Running makensis...
"%NSIS_DIR%\makensis.exe" installer.nsi
if errorlevel 1 (
    echo ERROR: NSIS installer build failed
    echo Check the installer.nsi file for errors
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS! Installer built successfully!
echo ========================================
echo.
echo Installer file: AudioPlayer_Setup.exe
echo Size: 
for %%A in (AudioPlayer_Setup.exe) do echo %%~zA bytes

echo.
echo You can now distribute AudioPlayer_Setup.exe
echo.
pause
