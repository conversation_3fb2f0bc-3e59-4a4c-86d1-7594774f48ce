#############################################################################
# Makefile for building: AudioPlayer
# Generated by qmake (3.1) (Qt 6.9.1)
# Project:  AudioPlayer.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_DEPRECATED_WARNINGS -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_MULTIMEDIA_LIB -DQT_SVG_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -O2 -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I. -Iinclude -I../../../../Qt/6.9.1/mingw_64/include -I../../../../Qt/6.9.1/mingw_64/include/QtWidgets -I../../../../Qt/6.9.1/mingw_64/include/QtMultimedia -I../../../../Qt/6.9.1/mingw_64/include/QtSvg -I../../../../Qt/6.9.1/mingw_64/include/QtGui -I../../../../Qt/6.9.1/mingw_64/include/QtNetwork -I../../../../Qt/6.9.1/mingw_64/include/QtCore -Ibuild/release/moc -Ibuild/release/ui -I/include -I../../../../Qt/6.9.1/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-s -Wl,-subsystem,windows -mthreads
LIBS        =        C:\Qt\6.9.1\mingw_64\lib\libQt6Widgets.a C:\Qt\6.9.1\mingw_64\lib\libQt6Multimedia.a C:\Qt\6.9.1\mingw_64\lib\libQt6Svg.a C:\Qt\6.9.1\mingw_64\lib\libQt6Gui.a C:\Qt\6.9.1\mingw_64\lib\libQt6Network.a C:\Qt\6.9.1\mingw_64\lib\libQt6Core.a build\release\obj\AudioPlayer_resource_res.o -lmingw32 C:\Qt\6.9.1\mingw_64\lib\libQt6EntryPoint.a -lshell32  
QMAKE         = C:\Qt\6.9.1\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = C:\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = C:\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = build\release\obj\AudioPlayer_resource_res.o
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = build\release\obj

####### Files

SOURCES       = src\main.cpp \
		src\mainwindow.cpp \
		src\audioplayer.cpp \
		src\playlist.cpp \
		src\playlistmodel.cpp \
		src\spotify\spotifyapi.cpp \
		src\spotify\spotifyauth.cpp \
		src\spotify\spotifytrack.cpp build\release\rcc\qrc_resources.cpp \
		build\release\moc\moc_mainwindow.cpp \
		build\release\moc\moc_audioplayer.cpp \
		build\release\moc\moc_playlist.cpp \
		build\release\moc\moc_playlistmodel.cpp \
		build\release\moc\moc_spotifyapi.cpp \
		build\release\moc\moc_spotifyauth.cpp \
		build\release\moc\moc_spotifytrack.cpp
OBJECTS       = build/release/obj/main.o \
		build/release/obj/mainwindow.o \
		build/release/obj/audioplayer.o \
		build/release/obj/playlist.o \
		build/release/obj/playlistmodel.o \
		build/release/obj/spotifyapi.o \
		build/release/obj/spotifyauth.o \
		build/release/obj/spotifytrack.o \
		build/release/obj/qrc_resources.o \
		build/release/obj/moc_mainwindow.o \
		build/release/obj/moc_audioplayer.o \
		build/release/obj/moc_playlist.o \
		build/release/obj/moc_playlistmodel.o \
		build/release/obj/moc_spotifyapi.o \
		build/release/obj/moc_spotifyauth.o \
		build/release/obj/moc_spotifytrack.o

DIST          =  include\mainwindow.h \
		include\audioplayer.h \
		include\playlist.h \
		include\playlistmodel.h \
		include\spotify\spotifyapi.h \
		include\spotify\spotifyauth.h \
		include\spotify\spotifytrack.h src\main.cpp \
		src\mainwindow.cpp \
		src\audioplayer.cpp \
		src\playlist.cpp \
		src\playlistmodel.cpp \
		src\spotify\spotifyapi.cpp \
		src\spotify\spotifyauth.cpp \
		src\spotify\spotifytrack.cpp
QMAKE_TARGET  = AudioPlayer
DESTDIR        = build\release\ #avoid trailing-slash linebreak
TARGET         = AudioPlayer.exe
DESTDIR_TARGET = build\release\AudioPlayer.exe

####### Build rules

first: all
all: Makefile.Release  build/release/AudioPlayer.exe

build/release/AudioPlayer.exe: C:/Qt/6.9.1/mingw_64/lib/libQt6Widgets.a C:/Qt/6.9.1/mingw_64/lib/libQt6Multimedia.a C:/Qt/6.9.1/mingw_64/lib/libQt6Svg.a C:/Qt/6.9.1/mingw_64/lib/libQt6Gui.a C:/Qt/6.9.1/mingw_64/lib/libQt6Network.a C:/Qt/6.9.1/mingw_64/lib/libQt6Core.a C:/Qt/6.9.1/mingw_64/lib/libQt6EntryPoint.a build/release/ui/ui_mainwindow.h $(OBJECTS) build/release/obj/AudioPlayer_resource_res.o
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @build\release\obj\object_script.AudioPlayer.Release $(LIBS)

build/release/obj/AudioPlayer_resource_res.o: AudioPlayer_resource.rc
	windres -i AudioPlayer_resource.rc -o build\release\obj\AudioPlayer_resource_res.o --include-dir=. $(DEFINES)

qmake: FORCE
	@$(QMAKE) -o Makefile.Release AudioPlayer.pro

qmake_all: FORCE

dist:
	$(ZIP) AudioPlayer.zip $(SOURCES) $(DIST) AudioPlayer.pro ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\spec_pre.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\device_config.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\common\sanitize.conf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\common\gcc-base.conf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\common\g++-base.conf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\common\windows-vulkan.conf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\common\g++-win32.conf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\common\windows-desktop.conf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\qconfig.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_freetype.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libpng.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_openxr_loader.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_linguist.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimedia.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimedia_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediaquick_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediawidgets.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_png_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlformat_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3d.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3d_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3deffects.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3deffects_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticles.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dutils.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dutils_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dxr.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dxr_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimeline.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimeline_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_shadertools.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_shadertools_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_spatialaudio.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_spatialaudio_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testinternals_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_tools_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\qt_functions.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\qt_config.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\win32-g++\qmake.conf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\spec_post.prf .qmake.stash ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\toolchain.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\default_pre.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\win32\default_pre.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\resolve_config.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds_post.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\default_post.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\build_pass.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\precompile_header.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\warn_on.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\permissions.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\qt.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\resources_functions.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\resources.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\moc.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\win32\opengl.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\uic.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\qmake_use.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\file_copies.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\win32\windows.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\testcase_targets.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\exceptions.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\yacc.prf ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\lex.prf AudioPlayer.pro resources\resources.qrc ..\..\..\..\Qt\6.9.1\mingw_64\lib\Qt6Widgets.prl ..\..\..\..\Qt\6.9.1\mingw_64\lib\Qt6Multimedia.prl ..\..\..\..\Qt\6.9.1\mingw_64\lib\Qt6Svg.prl ..\..\..\..\Qt\6.9.1\mingw_64\lib\Qt6Gui.prl ..\..\..\..\Qt\6.9.1\mingw_64\lib\Qt6Network.prl ..\..\..\..\Qt\6.9.1\mingw_64\lib\Qt6Core.prl ..\..\..\..\Qt\6.9.1\mingw_64\lib\Qt6EntryPoint.prl   resources\resources.qrc ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp include\mainwindow.h include\audioplayer.h include\playlist.h include\playlistmodel.h include\spotify\spotifyapi.h include\spotify\spotifyauth.h include\spotify\spotifytrack.h  src\main.cpp src\mainwindow.cpp src\audioplayer.cpp src\playlist.cpp src\playlistmodel.cpp src\spotify\spotifyapi.cpp src\spotify\spotifyauth.cpp src\spotify\spotifytrack.cpp ui\mainwindow.ui    

clean: compiler_clean 
	-$(DEL_FILE) build\release\obj\main.o build\release\obj\mainwindow.o build\release\obj\audioplayer.o build\release\obj\playlist.o build\release\obj\playlistmodel.o build\release\obj\spotifyapi.o build\release\obj\spotifyauth.o build\release\obj\spotifytrack.o build\release\obj\qrc_resources.o build\release\obj\moc_mainwindow.o build\release\obj\moc_audioplayer.o build\release\obj\moc_playlist.o build\release\obj\moc_playlistmodel.o build\release\obj\moc_spotifyapi.o build\release\obj\moc_spotifyauth.o build\release\obj\moc_spotifytrack.o
	-$(DEL_FILE) build\release\obj\AudioPlayer_resource_res.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: build/release/rcc/qrc_resources.cpp
compiler_rcc_clean:
	-$(DEL_FILE) build\release\rcc\qrc_resources.cpp
build/release/rcc/qrc_resources.cpp: resources/resources.qrc \
		../../../../Qt/6.9.1/mingw_64/bin/rcc.exe \
		resources/styles/dark_theme.qss \
		resources/icons/spotify_logo.svg \
		resources/icons/play.svg \
		resources/icons/pause.svg \
		resources/icons/previous.svg \
		resources/icons/volume.svg \
		resources/icons/next.svg \
		resources/icons/default_album_art.svg \
		resources/icons/add.svg \
		resources/icons/search.svg \
		resources/icons/app_icon.svg \
		resources/icons/volume_mute.svg \
		resources/icons/remove.svg \
		resources/icons/stop.svg
	C:\Qt\6.9.1\mingw_64\bin\rcc.exe -name resources --no-zstd resources\resources.qrc -o build\release\rcc\qrc_resources.cpp

compiler_moc_predefs_make_all: build/release/moc/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) build\release\moc\moc_predefs.h
build/release/moc/moc_predefs.h: ../../../../Qt/6.9.1/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o build\release\moc\moc_predefs.h ..\..\..\..\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: build/release/moc/moc_mainwindow.cpp build/release/moc/moc_audioplayer.cpp build/release/moc/moc_playlist.cpp build/release/moc/moc_playlistmodel.cpp build/release/moc/moc_spotifyapi.cpp build/release/moc/moc_spotifyauth.cpp build/release/moc/moc_spotifytrack.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) build\release\moc\moc_mainwindow.cpp build\release\moc\moc_audioplayer.cpp build\release\moc\moc_playlist.cpp build\release\moc\moc_playlistmodel.cpp build\release\moc\moc_spotifyapi.cpp build\release\moc\moc_spotifyauth.cpp build\release\moc\moc_spotifytrack.cpp
build/release/moc/moc_mainwindow.cpp: include/mainwindow.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QRect \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSize \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QSlider \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QListView \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlistview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QListWidget \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QListWidgetItem \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QFileDialog \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QMessageBox \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QKeyEvent \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDragEnterEvent \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDropEvent \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QCloseEvent \
		include/audioplayer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/QMediaPlayer \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qmediaplayer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		include/playlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFileInfo \
		include/playlistmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QAbstractListModel \
		include/spotify/spotifyauth.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QString \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFlags \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QIODevice \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QDialog \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDesktopServices \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		include/spotify/spotifyapi.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		include/spotify/spotifytrack.h \
		build/release/moc/moc_predefs.h \
		../../../../Qt/6.9.1/mingw_64/bin/moc.exe
	C:\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include "C:/Users/<USER>/Desktop/audio player/build/release/moc/moc_predefs.h" -IC:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -I"C:/Users/<USER>/Desktop/audio player" -I"C:/Users/<USER>/Desktop/audio player/include" -IC:/Qt/6.9.1/mingw_64/include -IC:/Qt/6.9.1/mingw_64/include/QtWidgets -IC:/Qt/6.9.1/mingw_64/include/QtMultimedia -IC:/Qt/6.9.1/mingw_64/include/QtSvg -IC:/Qt/6.9.1/mingw_64/include/QtGui -IC:/Qt/6.9.1/mingw_64/include/QtNetwork -IC:/Qt/6.9.1/mingw_64/include/QtCore -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IC:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include include\mainwindow.h -o build\release\moc\moc_mainwindow.cpp

build/release/moc/moc_audioplayer.cpp: include/audioplayer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/QMediaPlayer \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qmediaplayer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		build/release/moc/moc_predefs.h \
		../../../../Qt/6.9.1/mingw_64/bin/moc.exe
	C:\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include "C:/Users/<USER>/Desktop/audio player/build/release/moc/moc_predefs.h" -IC:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -I"C:/Users/<USER>/Desktop/audio player" -I"C:/Users/<USER>/Desktop/audio player/include" -IC:/Qt/6.9.1/mingw_64/include -IC:/Qt/6.9.1/mingw_64/include/QtWidgets -IC:/Qt/6.9.1/mingw_64/include/QtMultimedia -IC:/Qt/6.9.1/mingw_64/include/QtSvg -IC:/Qt/6.9.1/mingw_64/include/QtGui -IC:/Qt/6.9.1/mingw_64/include/QtNetwork -IC:/Qt/6.9.1/mingw_64/include/QtCore -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IC:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include include\audioplayer.h -o build\release\moc\moc_audioplayer.cpp

build/release/moc/moc_playlist.cpp: include/playlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFileInfo \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		build/release/moc/moc_predefs.h \
		../../../../Qt/6.9.1/mingw_64/bin/moc.exe
	C:\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include "C:/Users/<USER>/Desktop/audio player/build/release/moc/moc_predefs.h" -IC:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -I"C:/Users/<USER>/Desktop/audio player" -I"C:/Users/<USER>/Desktop/audio player/include" -IC:/Qt/6.9.1/mingw_64/include -IC:/Qt/6.9.1/mingw_64/include/QtWidgets -IC:/Qt/6.9.1/mingw_64/include/QtMultimedia -IC:/Qt/6.9.1/mingw_64/include/QtSvg -IC:/Qt/6.9.1/mingw_64/include/QtGui -IC:/Qt/6.9.1/mingw_64/include/QtNetwork -IC:/Qt/6.9.1/mingw_64/include/QtCore -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IC:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include include\playlist.h -o build\release\moc\moc_playlist.cpp

build/release/moc/moc_playlistmodel.cpp: include/playlistmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QAbstractListModel \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		include/playlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFileInfo \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		build/release/moc/moc_predefs.h \
		../../../../Qt/6.9.1/mingw_64/bin/moc.exe
	C:\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include "C:/Users/<USER>/Desktop/audio player/build/release/moc/moc_predefs.h" -IC:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -I"C:/Users/<USER>/Desktop/audio player" -I"C:/Users/<USER>/Desktop/audio player/include" -IC:/Qt/6.9.1/mingw_64/include -IC:/Qt/6.9.1/mingw_64/include/QtWidgets -IC:/Qt/6.9.1/mingw_64/include/QtMultimedia -IC:/Qt/6.9.1/mingw_64/include/QtSvg -IC:/Qt/6.9.1/mingw_64/include/QtGui -IC:/Qt/6.9.1/mingw_64/include/QtNetwork -IC:/Qt/6.9.1/mingw_64/include/QtCore -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IC:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include include\playlistmodel.h -o build\release\moc\moc_playlistmodel.cpp

build/release/moc/moc_spotifyapi.cpp: include/spotify/spotifyapi.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QString \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QList \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFlags \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QIODevice \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		build/release/moc/moc_predefs.h \
		../../../../Qt/6.9.1/mingw_64/bin/moc.exe
	C:\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include "C:/Users/<USER>/Desktop/audio player/build/release/moc/moc_predefs.h" -IC:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -I"C:/Users/<USER>/Desktop/audio player" -I"C:/Users/<USER>/Desktop/audio player/include" -IC:/Qt/6.9.1/mingw_64/include -IC:/Qt/6.9.1/mingw_64/include/QtWidgets -IC:/Qt/6.9.1/mingw_64/include/QtMultimedia -IC:/Qt/6.9.1/mingw_64/include/QtSvg -IC:/Qt/6.9.1/mingw_64/include/QtGui -IC:/Qt/6.9.1/mingw_64/include/QtNetwork -IC:/Qt/6.9.1/mingw_64/include/QtCore -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IC:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include include\spotify\spotifyapi.h -o build\release\moc\moc_spotifyapi.cpp

build/release/moc/moc_spotifyauth.cpp: include/spotify/spotifyauth.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QString \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QList \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFlags \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QIODevice \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QDialog \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QRect \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSize \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDesktopServices \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		build/release/moc/moc_predefs.h \
		../../../../Qt/6.9.1/mingw_64/bin/moc.exe
	C:\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include "C:/Users/<USER>/Desktop/audio player/build/release/moc/moc_predefs.h" -IC:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -I"C:/Users/<USER>/Desktop/audio player" -I"C:/Users/<USER>/Desktop/audio player/include" -IC:/Qt/6.9.1/mingw_64/include -IC:/Qt/6.9.1/mingw_64/include/QtWidgets -IC:/Qt/6.9.1/mingw_64/include/QtMultimedia -IC:/Qt/6.9.1/mingw_64/include/QtSvg -IC:/Qt/6.9.1/mingw_64/include/QtGui -IC:/Qt/6.9.1/mingw_64/include/QtNetwork -IC:/Qt/6.9.1/mingw_64/include/QtCore -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IC:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include include\spotify\spotifyauth.h -o build\release\moc\moc_spotifyauth.cpp

build/release/moc/moc_spotifytrack.cpp: include/spotify/spotifytrack.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QString \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		build/release/moc/moc_predefs.h \
		../../../../Qt/6.9.1/mingw_64/bin/moc.exe
	C:\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include "C:/Users/<USER>/Desktop/audio player/build/release/moc/moc_predefs.h" -IC:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -I"C:/Users/<USER>/Desktop/audio player" -I"C:/Users/<USER>/Desktop/audio player/include" -IC:/Qt/6.9.1/mingw_64/include -IC:/Qt/6.9.1/mingw_64/include/QtWidgets -IC:/Qt/6.9.1/mingw_64/include/QtMultimedia -IC:/Qt/6.9.1/mingw_64/include/QtSvg -IC:/Qt/6.9.1/mingw_64/include/QtGui -IC:/Qt/6.9.1/mingw_64/include/QtNetwork -IC:/Qt/6.9.1/mingw_64/include/QtCore -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IC:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include include\spotify\spotifytrack.h -o build\release\moc\moc_spotifytrack.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: build/release/ui/ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) build\release\ui\ui_mainwindow.h
build/release/ui/ui_mainwindow.h: ui/mainwindow.ui \
		../../../../Qt/6.9.1/mingw_64/bin/uic.exe
	C:\Qt\6.9.1\mingw_64\bin\uic.exe ui\mainwindow.ui -o build\release\ui\ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

build/release/obj/main.o: src/main.cpp ../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QApplication \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QStyleFactory \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qstylefactory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QPalette \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QDir \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFile \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QIODevice \
		include/mainwindow.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QRect \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSize \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QSlider \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QListView \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlistview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QListWidget \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QListWidgetItem \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QFileDialog \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QMessageBox \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QKeyEvent \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDragEnterEvent \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDropEvent \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QCloseEvent \
		include/audioplayer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/QMediaPlayer \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qmediaplayer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		include/playlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFileInfo \
		include/playlistmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QAbstractListModel \
		include/spotify/spotifyauth.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QString \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFlags \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QDialog \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDesktopServices \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		include/spotify/spotifyapi.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		include/spotify/spotifytrack.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\main.o src\main.cpp

build/release/obj/mainwindow.o: src/mainwindow.cpp include/mainwindow.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QRect \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSize \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QSlider \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QListView \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlistview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QListWidget \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QListWidgetItem \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QFileDialog \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QMessageBox \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QKeyEvent \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDragEnterEvent \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDropEvent \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QCloseEvent \
		include/audioplayer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/QMediaPlayer \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qmediaplayer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		include/playlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFileInfo \
		include/playlistmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QAbstractListModel \
		include/spotify/spotifyauth.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QString \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFlags \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QIODevice \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QDialog \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDesktopServices \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		include/spotify/spotifyapi.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		include/spotify/spotifytrack.h \
		build/release/ui/ui_mainwindow.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QAction \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QIcon \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QApplication \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QLineEdit \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QMenu \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qmenu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QMenuBar \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qmenubar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QSpacerItem \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QSplitter \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qsplitter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QStatusBar \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSettings \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsettings.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QStandardPaths \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstandardpaths.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QDir \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QMimeData \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmimedata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\mainwindow.o src\mainwindow.cpp

build/release/obj/audioplayer.o: src/audioplayer.cpp include/audioplayer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/QMediaPlayer \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qmediaplayer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../Qt/6.9.1/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QDebug \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\audioplayer.o src\audioplayer.cpp

build/release/obj/playlist.o: src/playlist.cpp include/playlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFileInfo \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QTextStream \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QDir \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QRandomGenerator \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrandom.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\playlist.o src\playlist.cpp

build/release/obj/playlistmodel.o: src/playlistmodel.cpp include/playlistmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QAbstractListModel \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		include/playlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFileInfo \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QMimeData \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmimedata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QFont \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QColor \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\playlistmodel.o src\playlistmodel.cpp

build/release/obj/spotifyapi.o: src/spotify/spotifyapi.cpp include/spotify/spotifyapi.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QString \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QList \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFlags \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QIODevice \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		include/spotify/spotifyauth.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QDialog \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QRect \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSize \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDesktopServices \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		include/spotify/spotifytrack.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonDocument \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrlQuery \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurlquery.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\spotifyapi.o src\spotify\spotifyapi.cpp

build/release/obj/spotifyauth.o: src/spotify/spotifyauth.cpp include/spotify/spotifyauth.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QString \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QList \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QFlags \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QIODevice \
		../../../../Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QDialog \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QRect \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSize \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/QDesktopServices \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonDocument \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrlQuery \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurlquery.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QRandomGenerator \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrandom.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QCryptographicHash \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QMessageBox \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/QInputDialog \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qinputdialog.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h \
		../../../../Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		../../../../Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QDebug \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QDateTime
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\spotifyauth.o src\spotify\spotifyauth.cpp

build/release/obj/spotifytrack.o: src/spotify/spotifytrack.cpp include/spotify/spotifytrack.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QString \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QUrl \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QJsonDocument \
		../../../../Qt/6.9.1/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\spotifytrack.o src\spotify\spotifytrack.cpp

build/release/obj/qrc_resources.o: build/release/rcc/qrc_resources.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\qrc_resources.o build\release\rcc\qrc_resources.cpp

build/release/obj/moc_mainwindow.o: build/release/moc/moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\moc_mainwindow.o build\release\moc\moc_mainwindow.cpp

build/release/obj/moc_audioplayer.o: build/release/moc/moc_audioplayer.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\moc_audioplayer.o build\release\moc\moc_audioplayer.cpp

build/release/obj/moc_playlist.o: build/release/moc/moc_playlist.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\moc_playlist.o build\release\moc\moc_playlist.cpp

build/release/obj/moc_playlistmodel.o: build/release/moc/moc_playlistmodel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\moc_playlistmodel.o build\release\moc\moc_playlistmodel.cpp

build/release/obj/moc_spotifyapi.o: build/release/moc/moc_spotifyapi.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\moc_spotifyapi.o build\release\moc\moc_spotifyapi.cpp

build/release/obj/moc_spotifyauth.o: build/release/moc/moc_spotifyauth.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\moc_spotifyauth.o build\release\moc\moc_spotifyauth.cpp

build/release/obj/moc_spotifytrack.o: build/release/moc/moc_spotifytrack.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\release\obj\moc_spotifytrack.o build\release\moc\moc_spotifytrack.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:

