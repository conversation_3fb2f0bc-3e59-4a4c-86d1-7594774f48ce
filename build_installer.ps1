# Audio Player Installer Builder (PowerShell)
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Audio Player Installer Builder" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set paths (modify these if your installations are different)
$QT_DIR = "C:\Qt\6.9.1\mingw_64"
$MINGW_DIR = "C:\Qt\Tools\mingw1310_64"
$NSIS_DIR = "C:\Program Files (x86)\NSIS"

# Add Qt and MinGW to PATH
$env:PATH += ";$QT_DIR\bin;$MINGW_DIR\bin"

Write-Host "[1/5] Checking prerequisites..." -ForegroundColor Yellow

# Check if NSIS is installed
if (-not (Test-Path "$NSIS_DIR\makensis.exe")) {
    Write-Host "ERROR: NSIS not found" -ForegroundColor Red
    Write-Host "Expected location: $NSIS_DIR\makensis.exe" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install NSIS from: https://nsis.sourceforge.io/" -ForegroundColor Yellow
    Write-Host "Or check if NSIS is installed in a different location" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Qt is installed
if (-not (Test-Path "$QT_DIR\bin\qmake.exe")) {
    Write-Host "ERROR: Qt not found" -ForegroundColor Red
    Write-Host "Expected location: $QT_DIR\bin\qmake.exe" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please check your Qt installation path" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Prerequisites check passed!" -ForegroundColor Green
Write-Host ""

Write-Host "[2/5] Cleaning previous build..." -ForegroundColor Yellow
if (Test-Path "build") { Remove-Item -Recurse -Force "build" -ErrorAction SilentlyContinue }
if (Test-Path "debug") { Remove-Item -Recurse -Force "debug" -ErrorAction SilentlyContinue }
if (Test-Path "release") { Remove-Item -Recurse -Force "release" -ErrorAction SilentlyContinue }
Get-ChildItem -Name "Makefile*" | Remove-Item -Force -ErrorAction SilentlyContinue
if (Test-Path "AudioPlayer_Setup.exe") { Remove-Item -Force "AudioPlayer_Setup.exe" -ErrorAction SilentlyContinue }

Write-Host "[3/5] Building application..." -ForegroundColor Yellow
Write-Host "Running qmake..."
$qmakeResult = Start-Process -FilePath "qmake" -ArgumentList "AudioPlayer.pro" -Wait -PassThru -NoNewWindow
if ($qmakeResult.ExitCode -ne 0) {
    Write-Host "ERROR: qmake failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Running make..."
$makeResult = Start-Process -FilePath "mingw32-make" -ArgumentList "release" -Wait -PassThru -NoNewWindow
if ($makeResult.ExitCode -ne 0) {
    Write-Host "ERROR: make failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[4/5] Checking build result..." -ForegroundColor Yellow
if (-not (Test-Path "build\release\AudioPlayer.exe")) {
    Write-Host "ERROR: AudioPlayer.exe not found in build\release\" -ForegroundColor Red
    Write-Host "Build may have failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Application built successfully!" -ForegroundColor Green

Write-Host "[5/5] Building installer..." -ForegroundColor Yellow
Write-Host "Running NSIS makensis..."
$nsisResult = Start-Process -FilePath "$NSIS_DIR\makensis.exe" -ArgumentList "installer.nsi" -Wait -PassThru -NoNewWindow
if ($nsisResult.ExitCode -ne 0) {
    Write-Host "ERROR: NSIS installer build failed" -ForegroundColor Red
    Write-Host ""
    Write-Host "Possible issues:" -ForegroundColor Yellow
    Write-Host "- Check that all DLL paths in installer.nsi are correct" -ForegroundColor Yellow
    Write-Host "- Ensure all required Qt files exist" -ForegroundColor Yellow
    Write-Host "- Check NSIS script syntax" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "SUCCESS! Installer built successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

if (Test-Path "AudioPlayer_Setup.exe") {
    $fileSize = (Get-Item "AudioPlayer_Setup.exe").Length
    Write-Host "Installer file: AudioPlayer_Setup.exe" -ForegroundColor Cyan
    Write-Host "Size: $fileSize bytes" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "The installer is ready for distribution!" -ForegroundColor Green
} else {
    Write-Host "WARNING: AudioPlayer_Setup.exe not found" -ForegroundColor Yellow
    Write-Host "The installer build may have failed" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to exit"
