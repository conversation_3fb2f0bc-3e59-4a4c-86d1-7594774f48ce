#ifndef SPOTIFYAPI_H
#define SPOTIFYAPI_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonArray>
#include <QString>
#include <QUrl>

class SpotifyAuth;
class SpotifyTrack;

class SpotifyAPI : public QObject
{
    Q_OBJECT

public:
    explicit SpotifyAPI(SpotifyAuth *auth, QObject *parent = nullptr);

    // Search functionality
    void searchTracks(const QString &query, int limit = 20, int offset = 0);
    void searchArtists(const QString &query, int limit = 20, int offset = 0);
    void searchAlbums(const QString &query, int limit = 20, int offset = 0);
    void searchPlaylists(const QString &query, int limit = 20, int offset = 0);
    void searchAll(const QString &query, int limit = 10, int offset = 0);

    // Track information
    void getTrack(const QString &trackId);
    void getTracks(const QStringList &trackIds);
    void getAudioFeatures(const QString &trackId);
    void getAudioFeatures(const QStringList &trackIds);

    // User's library
    void getUserSavedTracks(int limit = 20, int offset = 0);
    void getUserPlaylists(int limit = 20, int offset = 0);
    void getPlaylistTracks(const QString &playlistId, int limit = 100, int offset = 0);

    // User profile
    void getCurrentUser();
    void getUserProfile(const QString &userId);

    // Recommendations
    void getRecommendations(const QStringList &seedTracks = {}, 
                          const QStringList &seedArtists = {}, 
                          const QStringList &seedGenres = {},
                          int limit = 20);

    // Utility
    bool isAuthenticated() const;
    void setRateLimitDelay(int milliseconds);

signals:
    // Search results
    void searchTracksFinished(const QList<SpotifyTrack*> &tracks, int total, int offset);
    void searchArtistsFinished(const QJsonArray &artists, int total, int offset);
    void searchAlbumsFinished(const QJsonArray &albums, int total, int offset);
    void searchPlaylistsFinished(const QJsonArray &playlists, int total, int offset);
    void searchAllFinished(const QJsonObject &results);

    // Track information
    void trackReceived(SpotifyTrack *track);
    void tracksReceived(const QList<SpotifyTrack*> &tracks);
    void audioFeaturesReceived(const QString &trackId, const QJsonObject &features);
    void audioFeaturesReceived(const QJsonArray &features);

    // User data
    void userSavedTracksReceived(const QList<SpotifyTrack*> &tracks, int total, int offset);
    void userPlaylistsReceived(const QJsonArray &playlists, int total, int offset);
    void playlistTracksReceived(const QString &playlistId, const QList<SpotifyTrack*> &tracks, int total, int offset);

    // User profile
    void currentUserReceived(const QJsonObject &user);
    void userProfileReceived(const QJsonObject &user);

    // Recommendations
    void recommendationsReceived(const QList<SpotifyTrack*> &tracks);

    // Error handling
    void apiError(const QString &endpoint, int statusCode, const QString &message);
    void networkError(const QString &endpoint, const QString &error);

private slots:
    void onReplyFinished();

private:
    enum RequestType {
        SearchTracks,
        SearchArtists,
        SearchAlbums,
        SearchPlaylists,
        SearchAll,
        GetTrack,
        GetTracks,
        GetAudioFeatures,
        GetAudioFeaturesMultiple,
        GetUserSavedTracks,
        GetUserPlaylists,
        GetPlaylistTracks,
        GetCurrentUser,
        GetUserProfile,
        GetRecommendations
    };

    struct PendingRequest {
        RequestType type;
        QNetworkReply *reply;
        QJsonObject metadata;
    };

    QNetworkRequest createAuthenticatedRequest(const QUrl &url);
    void makeRequest(RequestType type, const QUrl &url, const QJsonObject &metadata = {});
    void handleResponse(const PendingRequest &request, const QByteArray &data, int statusCode);
    
    void handleSearchTracksResponse(const QJsonObject &response, const QJsonObject &metadata);
    void handleSearchArtistsResponse(const QJsonObject &response, const QJsonObject &metadata);
    void handleSearchAlbumsResponse(const QJsonObject &response, const QJsonObject &metadata);
    void handleSearchPlaylistsResponse(const QJsonObject &response, const QJsonObject &metadata);
    void handleSearchAllResponse(const QJsonObject &response);
    void handleTrackResponse(const QJsonObject &response);
    void handleTracksResponse(const QJsonObject &response);
    void handleAudioFeaturesResponse(const QJsonObject &response, const QJsonObject &metadata);
    void handleUserSavedTracksResponse(const QJsonObject &response, const QJsonObject &metadata);
    void handleUserPlaylistsResponse(const QJsonObject &response, const QJsonObject &metadata);
    void handlePlaylistTracksResponse(const QJsonObject &response, const QJsonObject &metadata);
    void handleCurrentUserResponse(const QJsonObject &response);
    void handleUserProfileResponse(const QJsonObject &response);
    void handleRecommendationsResponse(const QJsonObject &response);

    QList<SpotifyTrack*> parseTracksFromItems(const QJsonArray &items);
    QList<SpotifyTrack*> parseTracksFromTracks(const QJsonArray &tracks);

    // Demo/simulation methods
    void simulateSearchResponse(const QJsonObject &metadata);

    SpotifyAuth *m_auth;
    QNetworkAccessManager *m_networkManager;
    QHash<QNetworkReply*, PendingRequest> m_pendingRequests;
    int m_rateLimitDelay;

    // Spotify API base URL
    static const QString SPOTIFY_API_BASE_URL;
};

#endif // SPOTIFYAPI_H
