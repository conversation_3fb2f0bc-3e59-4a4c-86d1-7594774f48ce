QT += core widgets multimedia svg

CONFIG += c++17

TARGET = AudioPlayer
TEMPLATE = app

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

# Include directories
INCLUDEPATH += include

# Source files
SOURCES += \
    src/main.cpp \
    src/mainwindow.cpp \
    src/audioplayer.cpp \
    src/playlist.cpp \
    src/playlistmodel.cpp

# Header files
HEADERS += \
    include/mainwindow.h \
    include/audioplayer.h \
    include/playlist.h \
    include/playlistmodel.h

# UI files
FORMS += \
    ui/mainwindow.ui

# Resources
RESOURCES += \
    resources/resources.qrc

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

# Windows specific settings
win32 {
    VERSION = *******
    QMAKE_TARGET_COMPANY = "Audio Player Inc."
    QMAKE_TARGET_PRODUCT = "Audio Player"
    QMAKE_TARGET_DESCRIPTION = "Modern Qt Audio Player"
    QMAKE_TARGET_COPYRIGHT = "Copyright 2025"
}

# macOS specific settings
macx {
    # ICON = resources/icons/app_icon.icns
    # QMAKE_INFO_PLIST = Info.plist
}

# Output directories
CONFIG(debug, debug|release) {
    DESTDIR = build/debug
    OBJECTS_DIR = build/debug/obj
    MOC_DIR = build/debug/moc
    RCC_DIR = build/debug/rcc
    UI_DIR = build/debug/ui
}

CONFIG(release, debug|release) {
    DESTDIR = build/release
    OBJECTS_DIR = build/release/obj
    MOC_DIR = build/release/moc
    RCC_DIR = build/release/rcc
    UI_DIR = build/release/ui
}
