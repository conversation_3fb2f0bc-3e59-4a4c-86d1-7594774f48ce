#include "spotify/spotifyauth.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QUrlQuery>
#include <QRandomGenerator>
#include <QCryptographicHash>
#include <QDesktopServices>
#include <QMessageBox>
#include <QInputDialog>
#include <QPushButton>
#include <QAbstractButton>
#include <QDebug>
#include <QDateTime>

// Spotify API endpoints
const QString SpotifyAuth::SPOTIFY_AUTH_URL = "https://accounts.spotify.com/authorize";
const QString SpotifyAuth::SPOTIFY_TOKEN_URL = "https://accounts.spotify.com/api/token";
const QString SpotifyAuth::DEFAULT_REDIRECT_URI = "https://example.github.io/spotify-callback";
const QStringList SpotifyAuth::DEFAULT_SCOPES = {
    "user-read-private",
    "user-read-email", 
    "playlist-read-private",
    "playlist-read-collaborative",
    "user-library-read",
    "streaming"
};

SpotifyAuth::SpotifyAuth(QObject *parent)
    : QObject(parent)
    , m_redirectUri(DEFAULT_REDIRECT_URI)
    , m_scopes(DEFAULT_SCOPES)
    , m_authenticated(false)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_currentReply(nullptr)
    , m_autoRefreshTimer(new QTimer(this))
{
    connect(m_autoRefreshTimer, &QTimer::timeout, this, &SpotifyAuth::onAutoRefreshTimer);
}

SpotifyAuth::~SpotifyAuth()
{
}

void SpotifyAuth::setClientId(const QString &clientId)
{
    m_clientId = clientId;
}

void SpotifyAuth::setClientSecret(const QString &clientSecret)
{
    m_clientSecret = clientSecret;
}

void SpotifyAuth::setRedirectUri(const QString &redirectUri)
{
    m_redirectUri = redirectUri;
}

void SpotifyAuth::setScopes(const QStringList &scopes)
{
    m_scopes = scopes;
}

bool SpotifyAuth::isAuthenticated() const
{
    return m_authenticated && !isTokenExpired();
}

QString SpotifyAuth::accessToken() const
{
    return m_accessToken;
}

QString SpotifyAuth::refreshToken() const
{
    return m_refreshToken;
}

QDateTime SpotifyAuth::tokenExpiryTime() const
{
    return m_tokenExpiryTime;
}

int SpotifyAuth::tokenExpiresIn() const
{
    if (!m_authenticated) return 0;
    return QDateTime::currentDateTime().secsTo(m_tokenExpiryTime);
}

void SpotifyAuth::startAuthentication()
{
    if (m_clientId.isEmpty()) {
        emit authenticationFailed("Client ID not set. Please configure your Spotify app credentials.");
        return;
    }

    // Generate PKCE parameters for security
    m_codeVerifier = generateCodeVerifier();
    m_codeChallenge = generateCodeChallenge(m_codeVerifier);

    // Build authorization URL and open in browser
    QUrl authUrl = buildAuthorizationUrl();

    // Show instructions to user
    QMessageBox::information(nullptr, "Spotify Authentication",
        QString("IMPORTANT: Make sure your Spotify app redirect URI is set to:\n\n"
               "%1\n\n"
               "Your browser will open to Spotify's login page.\n"
               "After signing in, you'll be redirected to a page that may show an error.\n"
               "That's normal! Just copy the entire URL from your browser's address bar.\n\n"
               "Click OK to continue...").arg(m_redirectUri));

    // Open the authorization URL in the default browser
    QDesktopServices::openUrl(authUrl);

    // Ask user to paste the redirect URL
    bool ok;
    QString redirectUrl = QInputDialog::getText(nullptr,
        "Spotify Authorization",
        "After signing in to Spotify, copy the URL from your browser and paste it here:\n\n"
        "The URL should start with 'https://example.github.io/spotify-callback?code=...'",
        QLineEdit::Normal, "", &ok);

    if (ok && !redirectUrl.isEmpty()) {
        // Extract authorization code from the URL
        QUrl url(redirectUrl);
        QUrlQuery query(url);

        if (query.hasQueryItem("code")) {
            QString code = query.queryItemValue("code");
            exchangeCodeForTokens(code);
        } else if (query.hasQueryItem("error")) {
            QString error = query.queryItemValue("error");
            emit authenticationFailed("Authorization error: " + error);
        } else {
            emit authenticationFailed("No authorization code found in URL");
        }
    } else {
        emit authenticationFailed("Authentication cancelled by user");
    }
}

void SpotifyAuth::logout()
{
    m_authenticated = false;
    m_accessToken.clear();
    m_refreshToken.clear();
    m_tokenExpiryTime = QDateTime();
    
    stopAutoRefresh();
    emit loggedOut();
}

void SpotifyAuth::refreshAccessToken()
{
    if (m_refreshToken.isEmpty()) {
        emit tokenRefreshFailed("No refresh token available");
        return;
    }

    QNetworkRequest request{QUrl(SPOTIFY_TOKEN_URL)};
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");

    QUrlQuery params;
    params.addQueryItem("grant_type", "refresh_token");
    params.addQueryItem("refresh_token", m_refreshToken);
    params.addQueryItem("client_id", m_clientId);

    QByteArray data = params.toString(QUrl::FullyEncoded).toUtf8();
    
    m_currentReply = m_networkManager->post(request, data);
    connect(m_currentReply, &QNetworkReply::finished, this, &SpotifyAuth::onRefreshReplyFinished);
}

void SpotifyAuth::setTokens(const QString &accessToken, const QString &refreshToken, int expiresIn)
{
    m_accessToken = accessToken;
    m_refreshToken = refreshToken;
    m_tokenExpiryTime = QDateTime::currentDateTime().addSecs(expiresIn);
    m_authenticated = true;
    
    setupAutoRefresh();
}

bool SpotifyAuth::isTokenExpired() const
{
    return QDateTime::currentDateTime() >= m_tokenExpiryTime;
}

bool SpotifyAuth::isTokenExpiringSoon(int secondsThreshold) const
{
    return QDateTime::currentDateTime().addSecs(secondsThreshold) >= m_tokenExpiryTime;
}

void SpotifyAuth::handleAuthorizationCode(const QString &code)
{
    exchangeCodeForTokens(code);
}

void SpotifyAuth::onAuthReplyFinished()
{
    if (!m_currentReply) return;

    QByteArray response = m_currentReply->readAll();
    int statusCode = m_currentReply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();

    m_currentReply->deleteLater();
    m_currentReply = nullptr;

    if (statusCode == 200) {
        parseTokenResponse(response);
        // Authentication dialog not used in simplified version
        emit authenticationSucceeded();
    } else {
        qDebug() << "Authentication failed:" << response;
        emit authenticationFailed(QString("HTTP %1: %2").arg(statusCode).arg(QString(response)));
    }
}

void SpotifyAuth::onRefreshReplyFinished()
{
    if (!m_currentReply) return;

    QByteArray response = m_currentReply->readAll();
    int statusCode = m_currentReply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();

    m_currentReply->deleteLater();
    m_currentReply = nullptr;

    if (statusCode == 200) {
        parseTokenResponse(response);
        emit tokenRefreshed();
    } else {
        qDebug() << "Token refresh failed:" << response;
        emit tokenRefreshFailed(QString("HTTP %1: %2").arg(statusCode).arg(QString(response)));
    }
}



void SpotifyAuth::onAutoRefreshTimer()
{
    if (isTokenExpiringSoon()) {
        refreshAccessToken();
    }
}

void SpotifyAuth::setupAutoRefresh()
{
    // Refresh token 5 minutes before expiry
    int refreshInterval = qMax(300, tokenExpiresIn() - 300) * 1000; // Convert to milliseconds
    m_autoRefreshTimer->start(refreshInterval);
}

void SpotifyAuth::stopAutoRefresh()
{
    m_autoRefreshTimer->stop();
}

QString SpotifyAuth::generateCodeVerifier()
{
    const QString chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";
    QString verifier;
    
    for (int i = 0; i < 128; ++i) {
        verifier += chars[QRandomGenerator::global()->bounded(chars.length())];
    }
    
    return verifier;
}

QString SpotifyAuth::generateCodeChallenge(const QString &codeVerifier)
{
    QByteArray hash = QCryptographicHash::hash(codeVerifier.toUtf8(), QCryptographicHash::Sha256);
    return hash.toBase64(QByteArray::Base64UrlEncoding | QByteArray::OmitTrailingEquals);
}

QUrl SpotifyAuth::buildAuthorizationUrl()
{
    QUrl url(SPOTIFY_AUTH_URL);
    QUrlQuery query;
    
    query.addQueryItem("client_id", m_clientId);
    query.addQueryItem("response_type", "code");
    query.addQueryItem("redirect_uri", m_redirectUri);
    query.addQueryItem("scope", m_scopes.join(" "));
    query.addQueryItem("code_challenge_method", "S256");
    query.addQueryItem("code_challenge", m_codeChallenge);
    
    url.setQuery(query);
    return url;
}

void SpotifyAuth::exchangeCodeForTokens(const QString &code)
{
    QNetworkRequest request{QUrl(SPOTIFY_TOKEN_URL)};
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");

    QUrlQuery params;
    params.addQueryItem("grant_type", "authorization_code");
    params.addQueryItem("code", code);
    params.addQueryItem("redirect_uri", m_redirectUri);
    params.addQueryItem("client_id", m_clientId);
    params.addQueryItem("code_verifier", m_codeVerifier);

    QByteArray data = params.toString(QUrl::FullyEncoded).toUtf8();
    
    m_currentReply = m_networkManager->post(request, data);
    connect(m_currentReply, &QNetworkReply::finished, this, &SpotifyAuth::onAuthReplyFinished);
}

void SpotifyAuth::parseTokenResponse(const QByteArray &response)
{
    QJsonDocument doc = QJsonDocument::fromJson(response);
    QJsonObject obj = doc.object();

    QString accessToken = obj["access_token"].toString();
    QString refreshToken = obj["refresh_token"].toString();
    int expiresIn = obj["expires_in"].toInt();

    if (!accessToken.isEmpty()) {
        setTokens(accessToken, refreshToken.isEmpty() ? m_refreshToken : refreshToken, expiresIn);
    }
}

void SpotifyAuth::openAuthUrlInBrowser()
{
    QUrl authUrl = buildAuthorizationUrl();
    QDesktopServices::openUrl(authUrl);
}
